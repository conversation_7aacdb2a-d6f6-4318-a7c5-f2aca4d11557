namespace DIFA.ApiClients.FoodNet.Contract;

public class MissionDto
{
    public long MissionId { get; set; } // Changed to long to match entity

    public string MissionNb { get; set; }

    public DateTime? PlannedStartDate { get; set; } // Made nullable to match entity

    public DateTime? PlannedEndDate { get; set; } // Made nullable to match entity

    public DateTime? ActualStartDate { get; set; } // Made nullable to match entity

    public DateTime? ActualEndDate { get; set; } // Made nullable to match entity

    public string? LCE { get; set; } // This will be populated from PCE_CONTROLEUR

    public LCEDto? LceDto { get; set; } // Made nullable
}
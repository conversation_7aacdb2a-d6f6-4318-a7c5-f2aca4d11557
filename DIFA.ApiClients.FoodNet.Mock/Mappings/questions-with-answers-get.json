{"Guid": "questions-with-answers-get", "Title": "Get Questions with Answers", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/questions/with-answers"}]}, "Methods": ["GET"], "Params": [{"Name": "templateVersionId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "9363"}]}, {"Name": "missionId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "1673"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"itemId": 1, "parentChapterTitle": "Hygiëne en Veiligheid", "parentTitle": "Algemene <PERSON>", "title": "<PERSON><PERSON>jn de werkoppervlakken schoon?", "questionType": "<PERSON><PERSON>/<PERSON>ee", "questionTypeId": 1, "templateVersionId": 9363, "answer": {"id": 1, "templateVersionId": 9363, "questionResultId": 1, "result": "<PERSON>a", "scoreId": 1, "missionId": "1673"}}, {"itemId": 2, "parentChapterTitle": "Hygiëne en Veiligheid", "parentTitle": "Algemene <PERSON>", "title": "Wordt er gebruik gemaakt van geschikte reinigingsmiddelen?", "questionType": "<PERSON><PERSON>/<PERSON>ee", "questionTypeId": 1, "templateVersionId": 9363, "answer": {"id": 2, "templateVersionId": 9363, "questionResultId": 2, "result": "<PERSON><PERSON>", "scoreId": 2, "missionId": "1673"}}, {"itemId": 3, "parentChapterTitle": "Temperatuurcontrole", "parentTitle": "<PERSON><PERSON><PERSON>", "title": "Wordt de koelketen gerespecteerd?", "questionType": "Ja/<PERSON>ee/N.v.t.", "questionTypeId": 2, "templateVersionId": 9363, "answer": {"id": 3, "templateVersionId": 9363, "questionResultId": 3, "result": "<PERSON>a", "scoreId": 1, "missionId": "1673"}}, {"itemId": 4, "parentChapterTitle": "Temperatuurcontrole", "parentTitle": "<PERSON><PERSON><PERSON>", "title": "Wat is de gemeten temperatuur?", "questionType": "Numeriek", "questionTypeId": 3, "templateVersionId": 9363, "answer": {"id": 4, "templateVersionId": 9363, "questionResultId": 4, "result": "4.2°C", "scoreId": 1, "missionId": "1673"}}]}}
{"Guid": "answers-update", "Title": "Update Answer", "Request": {"Path": {"Matchers": [{"Name": "RegexMatcher", "Pattern": "^/api/answers/(\\d+)$"}]}, "Methods": ["PUT"], "Headers": [{"Name": "Content-Type", "Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "application/json*"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": {"id": 1, "templateVersionId": 9363, "questionResultId": 1, "result": "Bijgewerkt antwoord", "scoreId": 2, "missionId": "1673"}}}
{"Guid": "matrices-level4-get-all", "Title": "Get All Matrix Level 4", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/matrices/level4"}]}, "Methods": ["GET"]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"matrixId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "descriptionNL": "Matrix Level 4 Test 1 NL", "descriptionFR": "Matrix Level 4 Test 1 FR", "level": 4}, {"matrixId": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "descriptionNL": "Matrix Level 4 Test 2 NL", "descriptionFR": "Matrix Level 4 Test 2 FR", "level": 4}, {"matrixId": "c3d4e5f6-g7h8-9012-cdef-************", "descriptionNL": "Vlees en vleesproducten", "descriptionFR": "Viande et produits carnés", "level": 4}, {"matrixId": "d4e5f6g7-h8i9-0123-def0-************", "descriptionNL": "<PERSON><PERSON>vel en zuivelproducten", "descriptionFR": "Produits laitiers", "level": 4}]}}
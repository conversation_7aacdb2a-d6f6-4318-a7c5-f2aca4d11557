{"Guid": "missions-get-by-operator", "Title": "Get Missions by Operator ID", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/missions"}]}, "Methods": ["GET"], "Params": [{"Name": "operatorId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "2087535"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"missionId": 1673, "missionNb": "2462/21/083243", "plannedStartDate": "2024-01-15T08:00:00Z", "plannedEndDate": "2024-01-15T17:00:00Z", "actualStartDate": "2024-01-15T08:15:00Z", "actualEndDate": "2024-01-15T16:45:00Z", "lce": "LCE001", "lceDto": {"serviceCode": "LCE001", "descriptionNl": "Laboratorium Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Laboratoire 1 FR"}}, {"missionId": 1674, "missionNb": "2462/21/083244", "plannedStartDate": "2024-01-16T09:00:00Z", "plannedEndDate": "2024-01-16T18:00:00Z", "actualStartDate": "2024-01-16T09:10:00Z", "actualEndDate": "2024-01-16T17:30:00Z", "lce": "LCE002", "lceDto": {"serviceCode": "LCE002", "descriptionNl": "Laboratorium Controle Eenheid 2 NL", "descriptionFr": "Unité de Contrôle Laboratoire 2 FR"}}]}}
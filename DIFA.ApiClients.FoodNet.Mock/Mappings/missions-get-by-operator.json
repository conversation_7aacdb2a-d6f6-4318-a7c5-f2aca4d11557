{"Guid": "missions-get-by-operator", "Title": "Get Missions by Operator ID", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/missions"}]}, "Methods": ["GET"], "Params": [{"Name": "operatorId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "2087535"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"missionId": 1673, "missionNb": "y5z6a7b8-c9d0-1234-yzab-************", "plannedStartDate": "2024-01-15T08:00:00Z", "plannedEndDate": "2024-01-15T17:00:00Z", "actualStartDate": "2024-01-15T08:15:00Z", "actualEndDate": "2024-01-15T16:45:00Z", "lce": "i9j0k1l2-m3n4-5678-ijkl-************", "lceDto": {"serviceCode": "i9j0k1l2-m3n4-5678-ijkl-************", "descriptionNl": "Laboratorium Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Laboratoire 1 FR"}}, {"missionId": 1674, "missionNb": "z6a7b8c9-d0e1-2345-zabc-************", "plannedStartDate": "2024-01-16T09:00:00Z", "plannedEndDate": "2024-01-16T18:00:00Z", "actualStartDate": "2024-01-16T09:10:00Z", "actualEndDate": "2024-01-16T17:30:00Z", "lce": "j0k1l2m3-n4o5-6789-jklm-012345678901", "lceDto": {"serviceCode": "j0k1l2m3-n4o5-6789-jklm-012345678901", "descriptionNl": "Laboratorium Controle Eenheid 2 NL", "descriptionFr": "Unité de Contrôle Laboratoire 2 FR"}}]}}
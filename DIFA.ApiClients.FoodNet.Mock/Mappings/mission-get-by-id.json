{"Guid": "mission-get-by-id", "Title": "Get Mission by ID", "Request": {"Path": {"Matchers": [{"Name": "RegexMatcher", "Pattern": "^/api/missions/(\\d+)$"}]}, "Methods": ["GET"]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": {"missionId": 1673, "missionNb": "2462/21/083243", "plannedStartDate": "2024-01-15T08:00:00Z", "plannedEndDate": "2024-01-15T17:00:00Z", "actualStartDate": "2024-01-15T08:15:00Z", "actualEndDate": "2024-01-15T16:45:00Z", "lce": "LCE001", "lceDto": {"serviceCode": "LCE001", "descriptionNl": "Laboratorium Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Laboratoire 1 FR"}}}}
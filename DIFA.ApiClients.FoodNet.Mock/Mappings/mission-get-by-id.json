{"Guid": "mission-get-by-id", "Title": "Get Mission by ID", "Request": {"Path": {"Matchers": [{"Name": "RegexMatcher", "Pattern": "^/api/missions/(\\d+)$"}]}, "Methods": ["GET"]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": {"missionId": 1673, "missionNb": "y5z6a7b8-c9d0-1234-yzab-************", "plannedStartDate": "2024-01-15T08:00:00Z", "plannedEndDate": "2024-01-15T17:00:00Z", "actualStartDate": "2024-01-15T08:15:00Z", "actualEndDate": "2024-01-15T16:45:00Z", "lce": "i9j0k1l2-m3n4-5678-ijkl-************", "lceDto": {"serviceCode": "i9j0k1l2-m3n4-5678-ijkl-************", "descriptionNl": "Laboratorium Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Laboratoire 1 FR"}}}}
{"Guid": "samples-get-by-operator", "Title": "Get Samples by Operator ID", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/samples"}]}, "Methods": ["GET"], "Params": [{"Name": "operatorId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "2087535"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"sampleNumber": "SAMPLE001", "controlId": "CTRL001", "dateSample": "2024-01-15T10:30:00Z", "result": "CONFORM", "matrixNiv4Id": "M4_001", "matrixNiv5Id": "M5_001", "operatorId": 2087535, "matrixNiv4": {"matrixId": "M4_001", "descriptionNL": "Matrix Level 4 Test 1 NL", "descriptionFR": "Matrix Level 4 Test 1 FR", "level": 4}, "matrixNiv5": {"matrixId": "M5_001", "descriptionNL": "Matrix Level 5 Test 1 NL", "descriptionFR": "Matrix Level 5 Test 1 FR", "level": 5}}, {"sampleNumber": "SAMPLE002", "controlId": "CTRL002", "dateSample": "2024-01-15T14:15:00Z", "result": "NON-CONFORM", "matrixNiv4Id": "M4_002", "matrixNiv5Id": "M5_002", "operatorId": 2087535, "matrixNiv4": {"matrixId": "M4_002", "descriptionNL": "Matrix Level 4 Test 2 NL", "descriptionFR": "Matrix Level 4 Test 2 FR", "level": 4}, "matrixNiv5": {"matrixId": "M5_002", "descriptionNL": "Matrix Level 5 Test 2 NL", "descriptionFR": "Matrix Level 5 Test 2 FR", "level": 5}}]}}
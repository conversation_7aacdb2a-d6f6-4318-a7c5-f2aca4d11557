{"Guid": "samples-get-by-operator", "Title": "Get Samples by Operator ID", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/samples"}]}, "Methods": ["GET"], "Params": [{"Name": "operatorId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "2087535"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"sampleNumber": "m3n4o5p6-q7r8-9012-mnop-************", "controlId": "n4o5p6q7-r8s9-0123-nopq-************", "dateSample": "2024-01-15T10:30:00Z", "result": "CONFORM", "matrixNiv4Id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "matrixNiv5Id": "e5f6g7h8-i9j0-1234-efgh-************", "operatorId": 2087535, "matrixNiv4": {"matrixId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "descriptionNL": "Matrix Level 4 Test 1 NL", "descriptionFR": "Matrix Level 4 Test 1 FR", "level": 4}, "matrixNiv5": {"matrixId": "e5f6g7h8-i9j0-1234-efgh-************", "descriptionNL": "Matrix Level 5 Test 1 NL", "descriptionFR": "Matrix Level 5 Test 1 FR", "level": 5}}, {"sampleNumber": "o5p6q7r8-s9t0-1234-opqr-************", "controlId": "p6q7r8s9-t0u1-2345-pqrs-678901234567", "dateSample": "2024-01-15T14:15:00Z", "result": "NON-CONFORM", "matrixNiv4Id": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "matrixNiv5Id": "f6g7h8i9-j0k1-2345-fghi-678901234567", "operatorId": 2087535, "matrixNiv4": {"matrixId": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "descriptionNL": "Matrix Level 4 Test 2 NL", "descriptionFR": "Matrix Level 4 Test 2 FR", "level": 4}, "matrixNiv5": {"matrixId": "f6g7h8i9-j0k1-2345-fghi-678901234567", "descriptionNL": "Matrix Level 5 Test 2 NL", "descriptionFR": "Matrix Level 5 Test 2 FR", "level": 5}}]}}
{"Guid": "questions-get-by-template", "Title": "Get Questions by Template Version ID", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/questions"}]}, "Methods": ["GET"], "Params": [{"Name": "templateVersionId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "9363"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"itemId": 1, "parentChapterTitle": "Hygiëne en Veiligheid", "parentTitle": "Algemene <PERSON>", "title": "<PERSON><PERSON>jn de werkoppervlakken schoon?", "questionType": "<PERSON><PERSON>/<PERSON>ee", "questionTypeId": 1, "templateVersionId": 9363}, {"itemId": 2, "parentChapterTitle": "Hygiëne en Veiligheid", "parentTitle": "Algemene <PERSON>", "title": "Wordt er gebruik gemaakt van geschikte reinigingsmiddelen?", "questionType": "<PERSON><PERSON>/<PERSON>ee", "questionTypeId": 1, "templateVersionId": 9363}, {"itemId": 3, "parentChapterTitle": "Temperatuurcontrole", "parentTitle": "<PERSON><PERSON><PERSON>", "title": "Wordt de koelketen gerespecteerd?", "questionType": "Ja/<PERSON>ee/N.v.t.", "questionTypeId": 2, "templateVersionId": 9363}, {"itemId": 4, "parentChapterTitle": "Temperatuurcontrole", "parentTitle": "<PERSON><PERSON><PERSON>", "title": "Wat is de gemeten temperatuur?", "questionType": "Numeriek", "questionTypeId": 3, "templateVersionId": 9363}]}}
{"Guid": "answers-get-by-mission", "Title": "Get Answers by Mission ID", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/answers"}]}, "Methods": ["GET"], "Params": [{"Name": "missionId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "1673"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"id": 1, "templateVersionId": 9363, "questionResultId": 1, "result": "<PERSON>a", "scoreId": 1, "missionId": "1673"}, {"id": 2, "templateVersionId": 9363, "questionResultId": 2, "result": "<PERSON><PERSON>", "scoreId": 2, "missionId": "1673"}, {"id": 3, "templateVersionId": 9363, "questionResultId": 3, "result": "<PERSON>a", "scoreId": 1, "missionId": "1673"}, {"id": 4, "templateVersionId": 9363, "questionResultId": 4, "result": "4.2°C", "scoreId": 1, "missionId": "1673"}]}}
{"Guid": "matrices-level5-get-all", "Title": "Get All Matrix Level 5", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/matrices/level5"}]}, "Methods": ["GET"]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"matrixId": "e5f6g7h8-i9j0-1234-efgh-************", "descriptionNL": "Matrix Level 5 Test 1 NL", "descriptionFR": "Matrix Level 5 Test 1 FR", "level": 5}, {"matrixId": "f6g7h8i9-j0k1-2345-fghi-************", "descriptionNL": "Matrix Level 5 Test 2 NL", "descriptionFR": "Matrix Level 5 Test 2 FR", "level": 5}, {"matrixId": "g7h8i9j0-k1l2-3456-ghij-************", "descriptionNL": "Rundvlees vers", "descriptionFR": "Bœuf frais", "level": 5}, {"matrixId": "h8i9j0k1-l2m3-4567-hijk-************", "descriptionNL": "Varkensvlees vers", "descriptionFR": "<PERSON><PERSON> frais", "level": 5}]}}
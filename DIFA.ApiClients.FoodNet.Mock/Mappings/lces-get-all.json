{"Guid": "lces-get-all", "Title": "Get All LCEs", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/lces"}]}, "Methods": ["GET"]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"serviceCode": "i9j0k1l2-m3n4-5678-ijkl-************", "descriptionNl": "Laboratorium Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Laboratoire 1 FR"}, {"serviceCode": "j0k1l2m3-n4o5-6789-jklm-012345678901", "descriptionNl": "Laboratorium Controle Eenheid 2 NL", "descriptionFr": "Unité de Contrôle Laboratoire 2 FR"}, {"serviceCode": "k1l2m3n4-o5p6-7890-klmn-123456789012", "descriptionNl": "Post Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Post 1 FR"}, {"serviceCode": "l2m3n4o5-p6q7-8901-lmno-************", "descriptionNl": "Post Controle Eenheid 2 NL", "descriptionFr": "Unité de Contrôle Post 2 FR"}]}}
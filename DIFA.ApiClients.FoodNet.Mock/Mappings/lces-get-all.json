{"Guid": "lces-get-all", "Title": "Get All LCEs", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/lces"}]}, "Methods": ["GET"]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"serviceCode": "LCE001", "descriptionNl": "Laboratorium Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Laboratoire 1 FR"}, {"serviceCode": "LCE002", "descriptionNl": "Laboratorium Controle Eenheid 2 NL", "descriptionFr": "Unité de Contrôle Laboratoire 2 FR"}, {"serviceCode": "PCE001", "descriptionNl": "Post Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Post 1 FR"}, {"serviceCode": "PCE002", "descriptionNl": "Post Controle Eenheid 2 NL", "descriptionFr": "Unité de Contrôle Post 2 FR"}]}}
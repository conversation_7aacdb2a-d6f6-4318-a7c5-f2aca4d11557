{"Guid": "parameters-get-all", "Title": "Get All Parameters", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/parameters"}]}, "Methods": ["GET"]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"id": 1, "descriptionNL": "Salmonella spp.", "descriptionFR": "Salmonella spp."}, {"id": 2, "descriptionNL": "Listeria monocytogenes", "descriptionFR": "Listeria monocytogenes"}, {"id": 3, "descriptionNL": "Escherichia coli", "descriptionFR": "Escherichia coli"}, {"id": 4, "descriptionNL": "Campylobacter spp.", "descriptionFR": "Campylobacter spp."}, {"id": 5, "descriptionNL": "Enterobacteriaceae", "descriptionFR": "Enterobacteriaceae"}]}}
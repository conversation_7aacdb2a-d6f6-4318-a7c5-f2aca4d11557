{"Guid": "sample-details-get", "Title": "Get Sample Details", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/samples/details"}]}, "Methods": ["GET"], "Params": [{"Name": "operatorId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "2087535"}]}, {"Name": "controlId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "12345"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": [{"sampleId": "SAMPLE001", "sampleParameterId": "PARAM001", "sampleNb": "SN001", "controlId": "12345", "samplingId": "SAMPLING001", "parameterId": 1, "parameterResult": "< 10 CFU/g", "sampleResult": "CONFORM", "isExtra": false, "missionId": 1673, "parameter": {"id": 1, "descriptionNL": "Salmonella spp.", "descriptionFR": "Salmonella spp."}}, {"sampleId": "SAMPLE002", "sampleParameterId": "PARAM002", "sampleNb": "SN002", "controlId": "12345", "samplingId": "SAMPLING002", "parameterId": 2, "parameterResult": "> 100 CFU/g", "sampleResult": "NON-CONFORM", "isExtra": true, "missionId": 1673, "parameter": {"id": 2, "descriptionNL": "Listeria monocytogenes", "descriptionFR": "Listeria monocytogenes"}}]}}
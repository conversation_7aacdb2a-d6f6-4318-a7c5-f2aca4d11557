using WireMock.Server;
using WireMock.Settings;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace DIFA.ApiClients.FoodNet.Mock.Infrastructure;

public abstract class WireMockServerBase : IDisposable
{
    protected WireMockServer? Server { get; private set; }
    protected ILogger Logger { get; }
    public string BaseUrl => Server?.Url ?? throw new InvalidOperationException("Server not started");
    public int Port { get; private set; }

    protected WireMockServerBase(ILogger logger)
    {
        Logger = logger;
    }

    public virtual void Start(int? port = null)
    {
        var settings = new WireMockServerSettings
        {
            Port = port,
            StartAdminInterface = true,
            ReadStaticMappings = false,
            Logger = new WireMockConsoleLogger()
        };

        Server = WireMockServer.Start(settings);
        Port = Server.Port;
        
        Logger.LogInformation("WireMock server started on {BaseUrl}", BaseUrl);
        
        SetupMockEndpoints();
    }

    public virtual void Stop()
    {
        Server?.Stop();
        Server?.Dispose();
        Server = null;
        Logger.LogInformation("WireMock server stopped");
    }

    protected abstract void SetupMockEndpoints();

    protected string LoadJsonFromFile(string fileName)
    {
        var basePath = AppDomain.CurrentDomain.BaseDirectory;
        var filePath = Path.Combine(basePath, "MockData", fileName);
        
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"Mock data file not found: {filePath}");
        }
        
        return File.ReadAllText(filePath);
    }

    protected string SerializeToJson(object obj)
    {
        return JsonConvert.SerializeObject(obj, Formatting.Indented);
    }

    protected T DeserializeFromJson<T>(string json)
    {
        return JsonConvert.DeserializeObject<T>(json) ?? throw new InvalidOperationException($"Failed to deserialize JSON to {typeof(T).Name}");
    }

    public virtual void Reset()
    {
        Server?.Reset();
        SetupMockEndpoints();
        Logger.LogInformation("WireMock server reset and endpoints reconfigured");
    }

    public void Dispose()
    {
        Stop();
        GC.SuppressFinalize(this);
    }
}

public class WireMockConsoleLogger : IWireMockLogger
{
    public void Debug(string formatString, params object[] args)
    {
        Console.WriteLine($"[DEBUG] {string.Format(formatString, args)}");
    }

    public void Info(string formatString, params object[] args)
    {
        Console.WriteLine($"[INFO] {string.Format(formatString, args)}");
    }

    public void Warn(string formatString, params object[] args)
    {
        Console.WriteLine($"[WARN] {string.Format(formatString, args)}");
    }

    public void Error(string formatString, params object[] args)
    {
        Console.WriteLine($"[ERROR] {string.Format(formatString, args)}");
    }

    public void DebugRequestResponse(LogEntryModel logEntryModel, bool isAdminRequest)
    {
        Console.WriteLine($"[DEBUG] Request: {logEntryModel.RequestMessage?.Method} {logEntryModel.RequestMessage?.Url}");
        Console.WriteLine($"[DEBUG] Response: {logEntryModel.ResponseMessage?.StatusCode}");
    }

    public void Error(string formatString, Exception exception, params object[] args)
    {
        Console.WriteLine($"[ERROR] {string.Format(formatString, args)} - Exception: {exception.Message}");
    }
}

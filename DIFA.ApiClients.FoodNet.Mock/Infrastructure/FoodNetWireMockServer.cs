using Microsoft.Extensions.Logging;
using WireMock.RequestBuilders;
using WireMock.ResponseBuilders;
using WireMock.Matchers;
using System.Net;

namespace DIFA.ApiClients.FoodNet.Mock.Infrastructure;

public class FoodNetWireMockServer : WireMockServerBase
{
    public FoodNetWireMockServer(ILogger<FoodNetWireMockServer> logger) : base(logger)
    {
    }

    protected override void SetupMockEndpoints()
    {
        SetupMissionEndpoints();
        SetupLCEEndpoints();
        SetupMatrixEndpoints();
        SetupParameterEndpoints();
        SetupSampleEndpoints();
        SetupQuestionEndpoints();
        SetupAnswerEndpoints();
    }

    private void SetupMissionEndpoints()
    {
        // GET /api/missions?operatorId={operatorId}
        Server!.Given(Request.Create()
                .WithPath("/api/missions")
                .WithParam("operatorId")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var operatorId = req.GetType().GetProperty("Query")?.GetValue(req)?.ToString()?.Split('=')[1];
                    return LoadJsonFromFile($"missions-operator-{operatorId}.json");
                }));

        // GET /api/missions/{missionId}
        Server.Given(Request.Create()
                .WithPath(new RegexMatcher(@"^/api/missions/(\d+)$"))
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var path = req.GetType().GetProperty("Path")?.GetValue(req)?.ToString();
                    var missionId = path?.Split('/').Last();
                    try
                    {
                        return LoadJsonFromFile($"mission-{missionId}.json");
                    }
                    catch (FileNotFoundException)
                    {
                        return null; // Will result in 404
                    }
                }));

        // Handle mission not found
        Server.Given(Request.Create()
                .WithPath(new RegexMatcher(@"^/api/missions/(\d+)$"))
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.NotFound)
                .WithHeader("Content-Type", "application/json")
                .WithBody("{\"error\": \"Mission not found\"}"));
    }

    private void SetupLCEEndpoints()
    {
        // GET /api/lces
        Server!.Given(Request.Create()
                .WithPath("/api/lces")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBody(LoadJsonFromFile("lces.json")));

        // GET /api/lces/{serviceCode}
        Server.Given(Request.Create()
                .WithPath(new RegexMatcher(@"^/api/lces/(.+)$"))
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var path = req.GetType().GetProperty("Path")?.GetValue(req)?.ToString();
                    var serviceCode = path?.Split('/').Last();
                    try
                    {
                        return LoadJsonFromFile($"lce-{serviceCode}.json");
                    }
                    catch (FileNotFoundException)
                    {
                        return null;
                    }
                }));
    }

    private void SetupMatrixEndpoints()
    {
        // GET /api/matrices/level4
        Server!.Given(Request.Create()
                .WithPath("/api/matrices/level4")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBody(LoadJsonFromFile("matrices-level4.json")));

        // GET /api/matrices/level5
        Server.Given(Request.Create()
                .WithPath("/api/matrices/level5")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBody(LoadJsonFromFile("matrices-level5.json")));

        // GET /api/matrices/{matrixId}
        Server.Given(Request.Create()
                .WithPath(new RegexMatcher(@"^/api/matrices/(.+)$"))
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var path = req.GetType().GetProperty("Path")?.GetValue(req)?.ToString();
                    var matrixId = path?.Split('/').Last();
                    try
                    {
                        return LoadJsonFromFile($"matrix-{matrixId}.json");
                    }
                    catch (FileNotFoundException)
                    {
                        return null;
                    }
                }));
    }

    private void SetupParameterEndpoints()
    {
        // GET /api/parameters
        Server!.Given(Request.Create()
                .WithPath("/api/parameters")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBody(LoadJsonFromFile("parameters.json")));

        // GET /api/parameters/{parameterId}
        Server.Given(Request.Create()
                .WithPath(new RegexMatcher(@"^/api/parameters/(\d+)$"))
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var path = req.GetType().GetProperty("Path")?.GetValue(req)?.ToString();
                    var parameterId = path?.Split('/').Last();
                    try
                    {
                        return LoadJsonFromFile($"parameter-{parameterId}.json");
                    }
                    catch (FileNotFoundException)
                    {
                        return null;
                    }
                }));
    }

    private void SetupSampleEndpoints()
    {
        // GET /api/samples?operatorId={operatorId}
        Server!.Given(Request.Create()
                .WithPath("/api/samples")
                .WithParam("operatorId")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var operatorId = req.GetType().GetProperty("Query")?.GetValue(req)?.ToString()?.Split('=')[1];
                    return LoadJsonFromFile($"samples-operator-{operatorId}.json");
                }));

        // GET /api/samples/details?operatorId={operatorId}&controlId={controlId}
        Server.Given(Request.Create()
                .WithPath("/api/samples/details")
                .WithParam("operatorId")
                .WithParam("controlId")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var query = req.GetType().GetProperty("Query")?.GetValue(req)?.ToString();
                    var operatorId = ExtractQueryParam(query, "operatorId");
                    var controlId = ExtractQueryParam(query, "controlId");
                    return LoadJsonFromFile($"sample-details-{operatorId}-{controlId}.json");
                }));

        // GET /api/samples/{sampleId}
        Server.Given(Request.Create()
                .WithPath(new RegexMatcher(@"^/api/samples/(.+)$"))
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var path = req.GetType().GetProperty("Path")?.GetValue(req)?.ToString();
                    var sampleId = path?.Split('/').Last();
                    try
                    {
                        return LoadJsonFromFile($"sample-{sampleId}.json");
                    }
                    catch (FileNotFoundException)
                    {
                        return null;
                    }
                }));
    }

    private void SetupQuestionEndpoints()
    {
        // GET /api/questions?templateVersionId={templateVersionId}
        Server!.Given(Request.Create()
                .WithPath("/api/questions")
                .WithParam("templateVersionId")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var templateVersionId = req.GetType().GetProperty("Query")?.GetValue(req)?.ToString()?.Split('=')[1];
                    return LoadJsonFromFile($"questions-template-{templateVersionId}.json");
                }));

        // GET /api/questions/with-answers?templateVersionId={templateVersionId}&missionId={missionId}
        Server.Given(Request.Create()
                .WithPath("/api/questions/with-answers")
                .WithParam("templateVersionId")
                .WithParam("missionId")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var query = req.GetType().GetProperty("Query")?.GetValue(req)?.ToString();
                    var templateVersionId = ExtractQueryParam(query, "templateVersionId");
                    var missionId = ExtractQueryParam(query, "missionId");
                    return LoadJsonFromFile($"questions-with-answers-{templateVersionId}-{missionId}.json");
                }));
    }

    private void SetupAnswerEndpoints()
    {
        // GET /api/answers?missionId={missionId}
        Server!.Given(Request.Create()
                .WithPath("/api/answers")
                .WithParam("missionId")
                .UsingGet())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBodyAsJson((object req) =>
                {
                    var missionId = req.GetType().GetProperty("Query")?.GetValue(req)?.ToString()?.Split('=')[1];
                    return LoadJsonFromFile($"answers-mission-{missionId}.json");
                }));

        // POST /api/answers
        Server.Given(Request.Create()
                .WithPath("/api/answers")
                .UsingPost())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.Created)
                .WithHeader("Content-Type", "application/json")
                .WithBody(LoadJsonFromFile("answer-created.json")));

        // PUT /api/answers/{answerId}
        Server.Given(Request.Create()
                .WithPath(new RegexMatcher(@"^/api/answers/(\d+)$"))
                .UsingPut())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithHeader("Content-Type", "application/json")
                .WithBody(LoadJsonFromFile("answer-updated.json")));

        // DELETE /api/answers/{answerId}
        Server.Given(Request.Create()
                .WithPath(new RegexMatcher(@"^/api/answers/(\d+)$"))
                .UsingDelete())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.NoContent));
    }

    private static string? ExtractQueryParam(string? query, string paramName)
    {
        if (string.IsNullOrEmpty(query)) return null;
        
        var pairs = query.Split('&');
        foreach (var pair in pairs)
        {
            var keyValue = pair.Split('=');
            if (keyValue.Length == 2 && keyValue[0] == paramName)
            {
                return keyValue[1];
            }
        }
        return null;
    }
}

using DIFA.ApiClients.FoodNet.Contract;

namespace DIFA.ApiClients.FoodNet.Mock.Services;

/// <summary>
/// HTTP service interface for Mission operations
/// </summary>
public interface IMissionHttpService
{
    Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId, CancellationToken cancellationToken = default);
    Task<MissionDto?> GetMissionByIdAsync(long missionId, CancellationToken cancellationToken = default);
}

/// <summary>
/// HTTP service interface for LCE operations
/// </summary>
public interface ILCEHttpService
{
    Task<IEnumerable<LCEDto>> GetLCEsAsync(CancellationToken cancellationToken = default);
    Task<LCEDto?> GetLCEByServiceCodeAsync(string serviceCode, CancellationToken cancellationToken = default);
}

/// <summary>
/// HTTP service interface for Matrix operations
/// </summary>
public interface IMatrixHttpService
{
    Task<IEnumerable<MatrixDto>> GetMatrixLevel4Async(CancellationToken cancellationToken = default);
    Task<IEnumerable<MatrixDto>> GetMatrixLevel5Async(CancellationToken cancellationToken = default);
    Task<MatrixDto?> GetMatrixByIdAsync(string matrixId, CancellationToken cancellationToken = default);
}

/// <summary>
/// HTTP service interface for Parameter operations
/// </summary>
public interface IParameterHttpService
{
    Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken = default);
    Task<ParameterDto?> GetParameterByIdAsync(int parameterId, CancellationToken cancellationToken = default);
}

/// <summary>
/// HTTP service interface for Sample operations
/// </summary>
public interface ISampleHttpService
{
    Task<IEnumerable<SampleListDto>> GetSampleListAsync(int operatorId, CancellationToken cancellationToken = default);
    Task<IEnumerable<SampleDto>> GetSampleDetailsAsync(int operatorId, int controlId, CancellationToken cancellationToken = default);
    Task<SampleDto?> GetSampleByIdAsync(string sampleId, CancellationToken cancellationToken = default);
}

/// <summary>
/// HTTP service interface for Question operations
/// </summary>
public interface IQuestionHttpService
{
    Task<IEnumerable<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<QuestionWithAnswersDTO>> GetQuestionsWithAnswersAsync(int templateVersionId, string missionId, CancellationToken cancellationToken = default);
}

/// <summary>
/// HTTP service interface for Answer operations
/// </summary>
public interface IAnswerHttpService
{
    Task<IEnumerable<AnswerDto>> GetAnswersAsync(string missionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnswerDto>> GetAnswersByTemplateAsync(int templateVersionId, CancellationToken cancellationToken = default);
    Task<AnswerDto> CreateAnswerAsync(AnswerDto answerDto, CancellationToken cancellationToken = default);
    Task<AnswerDto> UpdateAnswerAsync(int answerId, AnswerDto answerDto, CancellationToken cancellationToken = default);
    Task DeleteAnswerAsync(int answerId, CancellationToken cancellationToken = default);
}

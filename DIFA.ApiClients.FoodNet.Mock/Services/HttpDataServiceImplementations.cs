using DIFA.ApiClients.FoodNet.Contract;
using Newtonsoft.Json;
using System.Text;

namespace DIFA.ApiClients.FoodNet.Mock.Services;

public class MissionHttpService : IMissionHttpService
{
    private readonly HttpClient _httpClient;

    public MissionHttpService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/missions?operatorId={operatorId}", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<MissionDto>>(json) ?? [];
    }

    public async Task<MissionDto?> GetMissionByIdAsync(long missionId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/missions/{missionId}", cancellationToken);
        
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            return null;
            
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<MissionDto>(json);
    }
}

public class LCEHttpService : ILCEHttpService
{
    private readonly HttpClient _httpClient;

    public LCEHttpService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IEnumerable<LCEDto>> GetLCEsAsync(CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync("/api/lces", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<LCEDto>>(json) ?? [];
    }

    public async Task<LCEDto?> GetLCEByServiceCodeAsync(string serviceCode, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/lces/{serviceCode}", cancellationToken);
        
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            return null;
            
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<LCEDto>(json);
    }
}

public class MatrixHttpService : IMatrixHttpService
{
    private readonly HttpClient _httpClient;

    public MatrixHttpService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IEnumerable<MatrixDto>> GetMatrixLevel4Async(CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync("/api/matrices/level4", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<MatrixDto>>(json) ?? [];
    }

    public async Task<IEnumerable<MatrixDto>> GetMatrixLevel5Async(CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync("/api/matrices/level5", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<MatrixDto>>(json) ?? [];
    }

    public async Task<MatrixDto?> GetMatrixByIdAsync(string matrixId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/matrices/{matrixId}", cancellationToken);
        
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            return null;
            
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<MatrixDto>(json);
    }
}

public class ParameterHttpService : IParameterHttpService
{
    private readonly HttpClient _httpClient;

    public ParameterHttpService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync("/api/parameters", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<ParameterDto>>(json) ?? [];
    }

    public async Task<ParameterDto?> GetParameterByIdAsync(int parameterId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/parameters/{parameterId}", cancellationToken);
        
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            return null;
            
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<ParameterDto>(json);
    }
}

public class SampleHttpService : ISampleHttpService
{
    private readonly HttpClient _httpClient;

    public SampleHttpService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IEnumerable<SampleListDto>> GetSampleListAsync(int operatorId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/samples?operatorId={operatorId}", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<SampleListDto>>(json) ?? [];
    }

    public async Task<IEnumerable<SampleDto>> GetSampleDetailsAsync(int operatorId, int controlId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/samples/details?operatorId={operatorId}&controlId={controlId}", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<SampleDto>>(json) ?? [];
    }

    public async Task<SampleDto?> GetSampleByIdAsync(string sampleId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/samples/{sampleId}", cancellationToken);
        
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            return null;
            
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<SampleDto>(json);
    }
}

public class QuestionHttpService : IQuestionHttpService
{
    private readonly HttpClient _httpClient;

    public QuestionHttpService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IEnumerable<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/questions?templateVersionId={templateVersionId}", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<QuestionDto>>(json) ?? [];
    }

    public async Task<IEnumerable<QuestionWithAnswersDTO>> GetQuestionsWithAnswersAsync(int templateVersionId, string missionId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/questions/with-answers?templateVersionId={templateVersionId}&missionId={missionId}", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<QuestionWithAnswersDTO>>(json) ?? [];
    }
}

public class AnswerHttpService : IAnswerHttpService
{
    private readonly HttpClient _httpClient;

    public AnswerHttpService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IEnumerable<AnswerDto>> GetAnswersAsync(string missionId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/answers?missionId={missionId}", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<AnswerDto>>(json) ?? [];
    }

    public async Task<IEnumerable<AnswerDto>> GetAnswersByTemplateAsync(int templateVersionId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync($"/api/answers?templateVersionId={templateVersionId}", cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<IEnumerable<AnswerDto>>(json) ?? [];
    }

    public async Task<AnswerDto> CreateAnswerAsync(AnswerDto answerDto, CancellationToken cancellationToken = default)
    {
        var json = JsonConvert.SerializeObject(answerDto);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PostAsync("/api/answers", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<AnswerDto>(responseJson) ?? throw new InvalidOperationException("Failed to deserialize created answer");
    }

    public async Task<AnswerDto> UpdateAnswerAsync(int answerId, AnswerDto answerDto, CancellationToken cancellationToken = default)
    {
        var json = JsonConvert.SerializeObject(answerDto);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PutAsync($"/api/answers/{answerId}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<AnswerDto>(responseJson) ?? throw new InvalidOperationException("Failed to deserialize updated answer");
    }

    public async Task DeleteAnswerAsync(int answerId, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.DeleteAsync($"/api/answers/{answerId}", cancellationToken);
        response.EnsureSuccessStatusCode();
    }
}

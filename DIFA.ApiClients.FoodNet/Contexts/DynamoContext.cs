using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Contexts;

public class DynamoContext : DbContext
{
    public DbSet<Question> Questions { get; set; }
    public DbSet<Answer> Answers { get; set; } 
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Question configuration
        modelBuilder.Entity<Question>(entity =>
        {
            entity.HasKey(e => e.ItemId);
            entity.ToTable("QUESTIONS");
            
            entity.Property(e => e.ItemId).HasColumnName("item_id");
            entity.Property(e => e.ParentChapterTitle).HasColumnName("hoofdstuk_ouder_titel");
            entity.Property(e => e.ParentTitle).HasColumnName("hoofdstuk_titel");
            entity.Property(e => e.Title).HasColumnName("titel");
            entity.Property(e => e.QuestionType).HasColumnName("type_vraag_omschrijving");
            entity.Property(e => e.QuestionTypeId).HasColumnName("type_antwoord_id");
            entity.Property(e => e.TemplateVersionId).HasColumnName("template_version_id");
        });
        
        // Answer configuration
        modelBuilder.Entity<Answer>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToTable("ANSWERS"); // Your actual SQL Server table name
            
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.TemplateVersionId).HasColumnName("template_version_id");
            entity.Property(e => e.QuestionResultId).HasColumnName("question_result_id");
            entity.Property(e => e.Result).HasColumnName("result");
            entity.Property(e => e.ScoreId).HasColumnName("score_id");
            entity.Property(e => e.MissionId).HasColumnName("mission_id");
            
            // Optional: Configure relationship with Question if needed
            entity.HasOne(a => a.Question)
                .WithMany()
                .HasForeignKey(a => a.QuestionResultId)
                .HasPrincipalKey(q => q.ItemId);
        });
    }
}
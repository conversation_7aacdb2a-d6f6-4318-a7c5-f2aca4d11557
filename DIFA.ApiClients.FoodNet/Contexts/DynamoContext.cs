using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Contexts;

public class DynamoContext : DbContext
{
    public DynamoContext() { }

    public DynamoContext(DbContextOptions<DynamoContext> options) : base(options) { }

    public DbSet<Question> Questions { get; init; }
    public DbSet<Answer> Answers { get; init; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Question configuration - Based on Questions.sql
        modelBuilder.Entity<Question>(entity =>
        {
            entity.HasKey(e => e.ItemId);
            entity.ToView("QUESTIONS_VIEW"); // This is based on a complex query from Questions.sql

            entity.Property(e => e.ItemId).HasColumnName("item_id");
            entity.Property(e => e.ParentChapterTitle).HasColumnName("hoofdstuk_ouder_titel");
            entity.Property(e => e.ParentTitle).HasColumnName("hoofdstuk_titel");
            entity.Property(e => e.Title).HasColumnName("titel");
            entity.Property(e => e.QuestionType).HasColumnName("type_vraag_omschrijving");
            entity.Property(e => e.QuestionTypeId).HasColumnName("type_antwoord_id");
            entity.Property(e => e.TemplateVersionId).HasColumnName("template_version_id");
        });

        // Answer configuration - Based on Results.sql structure
        modelBuilder.Entity<Answer>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToTable("VRAAGRESULTAAT", "FOODNET"); // FOODNET.VRAAGRESULTAAT table

            entity.Property(e => e.Id).HasColumnName("vraagresultaat_id");
            entity.Property(e => e.TemplateVersionId).HasColumnName("TEMPLATEVERSIE_ID");
            entity.Property(e => e.QuestionResultId).HasColumnName("vraagresultaat_id");
            entity.Property(e => e.Result).HasColumnName("RESULTAAT");
            entity.Property(e => e.ScoreId).HasColumnName("score_id");
            entity.Property(e => e.MissionId).HasColumnName("missionId");

            // Optional: Configure relationship with Question if needed
            entity.HasOne(a => a.Question)
                .WithMany()
                .HasForeignKey(a => a.QuestionResultId)
                .HasPrincipalKey(q => q.ItemId);
        });
    }
}
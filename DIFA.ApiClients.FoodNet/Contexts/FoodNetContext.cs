using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Contexts;

public class FoodNetContext : DbContext
{
    public DbSet<Sample> Samples { get; set; }
    public DbSet<SampleList> SampleLists { get; set; }
    public DbSet<Mission> Missions { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Sample configuration
        modelBuilder.Entity<Sample>(entity =>
        {
            entity.HasKey(e => e.SampleId);
            entity.ToTable("SAMPLES"); // Your actual Oracle table name
            
            entity.Property(e => e.SampleId).HasColumnName("SAMPLE_ID");
            entity.Property(e => e.SampleParameterId).HasColumnName("SAMPLE_PARAMETER_ID");
            entity.Property(e => e.SampleNb).HasColumnName("SAMPLE_NB");
            entity.Property(e => e.ControlId).HasColumnName("CONTROL_ID");
            entity.Property(e => e.<PERSON>Id).HasColumnName("SAMPLING_ID");
            entity.Property(e => e.ParameterId).HasColumnName("PARAMETER_ID");
            entity.Property(e => e.ParameterResult).HasColumnName("PARAMETER_RESULT");
            entity.Property(e => e.SampleResult).HasColumnName("SAMPLE_RESULT");
            entity.Property(e => e.IsExtra).HasColumnName("IS_EXTRA");
            entity.Property(e => e.MissionId).HasColumnName("MISSION_ID");
            
            entity.HasOne(s => s.Mission)
                .WithMany()
                .HasForeignKey(s => s.MissionId);
            
            entity.HasOne(s => s.Parameter)
                .WithMany(p => p.Samples)
                .HasForeignKey(s => s.ParameterId);
        });
        
        // SampleList configuration (probably a view)
        modelBuilder.Entity<SampleList>(entity =>
        {
            entity.HasNoKey(); // Assuming this is a view
            entity.ToTable("SAMPLE_LIST_VIEW"); // Your actual Oracle view name
            
            entity.Property(e => e.MONSTERNUMMER).HasColumnName("MONSTERNUMMER");
            entity.Property(e => e.CONTROLE_ID).HasColumnName("CONTROLE_ID");
            entity.Property(e => e.DATUMMONSTERNAME).HasColumnName("DATUMMONSTERNAME");
            entity.Property(e => e.RESULTAAT).HasColumnName("RESULTAAT");
            entity.Property(e => e.MATRIX_NIV_4_ID).HasColumnName("MATRIX_NIV_4_ID");
            entity.Property(e => e.MATRIX_NIV_5_ID).HasColumnName("MATRIX_NIV_5_ID");
            entity.Property(e => e.OperatorId).HasColumnName("OPERATOR_ID");
        });
        
        // Mission configuration
        modelBuilder.Entity<Mission>(entity =>
        {
            entity.HasKey(e => e.MissionId);
            entity.ToTable("MISSIONS");
            
            entity.Property(e => e.MissionId).HasColumnName("MISSION_ID");
            entity.Property(e => e.MissionNb).HasColumnName("MISSION_NB");
            entity.Property(e => e.PlannedStartDateHour).HasColumnName("PLANNED_START_DATE");
            entity.Property(e => e.PlannedEndDateHour).HasColumnName("PLANNED_END_DATE");
            entity.Property(e => e.ActualStartDateHour).HasColumnName("ACTUAL_START_DATE");
            entity.Property(e => e.ActualEndDateHour).HasColumnName("ACTUAL_END_DATE");
            entity.Property(e => e.OperatorId).HasColumnName("OPERATOR_ID");
            entity.Property(e => e.PceCodeOperator).HasColumnName("LCE");
        });
    }
}
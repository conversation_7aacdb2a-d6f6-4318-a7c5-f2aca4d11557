using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Contexts;

public class AlphaContext : DbContext
{
    public DbSet<LCE> LCEs { get; set; }
    public DbSet<PCE> PCEs { get; set; }
    public DbSet<Matrix> Matrices { get; set; }
    public DbSet<Parameter> Parameters { get; set; }
    public DbSet<MatrixLevel4> MatrixLevel4s { get; set; }
    public DbSet<MatrixLevel5> MatrixLevel5s { get; set; }
    public DbSet<LCEView> LCEViews { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // LCE configuration - Based on LCE.sql
        modelBuilder.Entity<LCE>(entity =>
        {
            entity.HasKey(e => e.ServiceCode);
            entity.ToTable("LCE", "ALPHA"); // ALPHA.LCE table

            entity.Property(e => e.ServiceCode).HasColumnName("DIENST_CODE");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");
        });

        // PCE configuration - Based on LCE.sql (part of union)
        modelBuilder.Entity<PCE>(entity =>
        {
            entity.HasKey(e => e.ServiceCode);
            entity.ToTable("PCE", "ALPHA"); // ALPHA.PCE table

            entity.Property(e => e.ServiceCode).HasColumnName("DIENST_CODE");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");
        });

        // Matrix Level 4 configuration - Based on MatrixLevel4.sql
        modelBuilder.Entity<Matrix>(entity =>
        {
            entity.HasKey(e => e.MatrixId);
            // Configure for both MATRIX_NIV_4 and MATRIX_NIV_5 tables
            // This will be handled by views or separate queries
            entity.ToTable("MATRIX_NIV_4", "ALPHA"); // Default to Level 4 table

            entity.Property(e => e.MatrixId).HasColumnName("MATRIX_NIV_4_ID");
            entity.Property(e => e.DescriptionNL).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFR).HasColumnName("OMSCHRIJVING_FR");
            entity.Property(e => e.Level).HasColumnName("MATRIX_LEVEL").HasDefaultValue(4);
        });

        // Parameter configuration - Based on Parameter.sql
        modelBuilder.Entity<Parameter>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToTable("PARAMETER", "ALPHA"); // ALPHA.PARAMETER table

            entity.Property(e => e.Id).HasColumnName("PARAMETER_ID");
            entity.Property(e => e.DescriptionNL).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFR).HasColumnName("OMSCHRIJVING_FR");
        });

        // MatrixLevel4 configuration - Based on MatrixLevel4.sql
        modelBuilder.Entity<MatrixLevel4>(entity =>
        {
            entity.HasKey(e => e.MatrixNiv4Id);
            entity.ToTable("MATRIX_NIV_4", "ALPHA");

            entity.Property(e => e.MatrixNiv4Id).HasColumnName("MATRIX_NIV_4_ID");
            entity.Property(e => e.DescriptionNL).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFR).HasColumnName("OMSCHRIJVING_FR");
        });

        // MatrixLevel5 configuration - Based on MatrixLevel5.sql
        modelBuilder.Entity<MatrixLevel5>(entity =>
        {
            entity.HasKey(e => e.MatrixNiv5Id);
            entity.ToTable("MATRIX_NIV_5", "ALPHA");

            entity.Property(e => e.MatrixNiv5Id).HasColumnName("MATRIX_NIV_5_ID");
            entity.Property(e => e.DescriptionNL).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFR).HasColumnName("OMSCHRIJVING_FR");
        });

        // LCEView configuration - Based on LCE.sql (union query)
        modelBuilder.Entity<LCEView>(entity =>
        {
            entity.HasNoKey(); // This is a view/query result
            entity.ToView("LCE_UNION_VIEW"); // We'll create a database view for this

            entity.Property(e => e.ServiceCode).HasColumnName("DIENST_CODE");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");
        });
    }
}
using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Contexts;

public class AlphaContext : DbContext
{
    public DbSet<LCE> LCEs { get; set; }
    public DbSet<Matrix> Matrices { get; set; }
    public DbSet<Parameter> Parameters { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // LCE configuration
        modelBuilder.Entity<LCE>(entity =>
        {
            entity.HasKey(e => e.ServiceCode);
            entity.ToTable("LCE_TABLE"); // Your actual Oracle table name
            
            entity.Property(e => e.ServiceCode).HasColumnName("DIENST_CODE");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");
        });
        
        // Matrix configuration
        modelBuilder.Entity<Matrix>(entity =>
        {
            entity.HasKey(e => e.MatrixId);
            entity.ToTable("MATRIX_TABLE"); // Your actual Oracle table name
            
            entity.Property(e => e.MatrixId).HasColumnName("ID");
            entity.Property(e => e.DescriptionNL).HasColumnName("DESCRIPTION_NL");
            entity.Property(e => e.DescriptionFR).HasColumnName("DESCRIPTION_FR");
            entity.Property(e => e.Level).HasColumnName("MATRIX_LEVEL");
        });
        
        // Parameter configuration
        modelBuilder.Entity<Parameter>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToTable("PARAMETER_TABLE"); // Your actual Oracle table name
            
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.DescriptionNL).HasColumnName("DESCRIPTION_NL");
            entity.Property(e => e.DescriptionFR).HasColumnName("DESCRIPTION_FR");
        });
    }
}
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace DIFA.ApiClients.FoodNet;

public static class FoodNetBootstrapper
{
    public static IServiceCollection AddFoodNetClient(this IServiceCollection services, 
        string foodNetConnectionString, 
        string alphaConnectionString, 
        string dynamoConnectionString)
    {
        // DbContexts
        services.AddDbContext<FoodNetContext>(options => options.UseOracle(foodNetConnectionString));
        services.AddDbContext<AlphaContext>(options => options.UseOracle(alphaConnectionString));
        services.AddDbContext<DynamoContext>(options => options.UseSqlServer(dynamoConnectionString));
        
        // Services
        services.AddTransient<ILCEService, IlCEService>();
        services.AddTransient<IMatrixService, MatrixService>();
        services.AddTransient<IParameterService, ParameterService>();
        services.AddTransient<ICheckListService, CheckListService>();
        services.AddTransient<IMissionService, MissionService>(); 
        // services.AddTransient<ISampleService, SampleService>();  
        services.AddTransient<IAnswerService, AnswerService>(); 
        
        // Main client
        services.AddTransient<IFoodNetClient, FoodNetClient>();
        
        return services;
    }
}
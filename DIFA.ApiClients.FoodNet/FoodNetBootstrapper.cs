using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace DIFA.ApiClients.FoodNet;

public static class FoodNetBootstrapper
{
    public static IServiceCollection AddFoodNetClient(this IServiceCollection services, 
        string foodNetConnectionString, 
        string alphaConnectionString, 
        string dynamoConnectionString)
    {
        // DbContexts
        services.AddDbContext<FoodNetContext>(options => options.UseOracle(foodNetConnectionString));
        services.AddDbContext<AlphaContext>(options => options.UseOracle(alphaConnectionString));
        services.AddDbContext<DynamoContext>(options => options.UseSqlServer(dynamoConnectionString));
        
        // Services
        services.AddTransient<ILCEService, Lce>();
        services.AddTransient<IMatrixService, Matrix>();
        services.AddTransient<IParameterService, Parameter>();
        services.AddTransient<ICheckListService, CheckListService>();
        services.AddTransient<IMissionService, Mission>(); 
        // services.AddTransient<ISampleService, SampleService>();  
        services.AddTransient<IAnswerService, AnswerService>(); 
        
        // Main client
        services.AddTransient<IFoodNetClient, FoodNetClient>();
        
        return services;
    }
}
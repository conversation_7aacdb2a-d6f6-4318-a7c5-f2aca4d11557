using DIFA.ApiClients.FoodNet.Contract;

namespace DIFA.ApiClients.FoodNet;

/// <summary>
/// The FoodNet Client interface
/// </summary>
public interface IFoodNetClient
{
    // Mission operations
    Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId, CancellationToken cancellationToken = default);
    Task<MissionDto> GetMissionByIdAsync(string missionId, CancellationToken cancellationToken = default);
    Task<byte[]> GenerateMissionPdfAsync(string missionId, int templateVersionId, CancellationToken cancellationToken = default);
    
    // Sample operations
    // Task<IEnumerable<SampleListDto>> GetSamplesAsync(int operatorId, CancellationToken cancellationToken = default);
    // Task<IEnumerable<SampleDto>> GetSampleDetailsAsync(int operatorId, int controlId, CancellationToken cancellationToken = default);
    // Task<SampleDto> GetSampleByIdAsync(string sampleId, CancellationToken cancellationToken = default);
    
    // LCE operations
    Task<IEnumerable<LCEDto>> GetLCEsAsync(CancellationToken cancellationToken = default);
    Task<LCEDto> GetLCEByServiceCodeAsync(string serviceCode, CancellationToken cancellationToken = default);
    
    // Matrix operations
    Task<IEnumerable<MatrixDto>> GetMatricesLevel4Async(CancellationToken cancellationToken = default);
    Task<IEnumerable<MatrixDto>> GetMatricesLevel5Async(CancellationToken cancellationToken = default);
    Task<IEnumerable<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken = default);
    Task<MatrixDto> GetMatrixByIdAsync(string matrixId, CancellationToken cancellationToken = default);
    
    // Parameter operations
    Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken = default);
    Task<ParameterDto> GetParameterByIdAsync(int parameterId, CancellationToken cancellationToken = default);
    
    // Checklist operations
    Task<IEnumerable<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnswerDto>> GetAnswersAsync(string missionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnswerDto>> GetAnswersByTemplateAsync(int templateVersionId, CancellationToken cancellationToken = default);
    Task<AnswerDto> CreateAnswerAsync(AnswerDto answerDto, CancellationToken cancellationToken = default);
    Task<AnswerDto> UpdateAnswerAsync(int id, AnswerDto answerDto, CancellationToken cancellationToken = default);
    Task DeleteAnswerAsync(int id, CancellationToken cancellationToken = default);
}
namespace DIFA.ApiClients.FoodNet.Helpers;

internal static class EmbeddedResourceHelper
{
    internal static async Task<string> ReadEmbeddedResourceAsync(string resourceName)
    {
        var assembly = typeof(EmbeddedResourceHelper).Assembly;
        await using var stream = assembly.GetManifestResourceStream(resourceName);
        using var reader = new StreamReader(stream);
        return await reader.ReadToEndAsync();
    }
}
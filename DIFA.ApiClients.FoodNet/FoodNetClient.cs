using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Entities;
using DIFA.ApiClients.FoodNet.Services;

namespace DIFA.ApiClients.FoodNet;

public class FoodNetClient : IFoodNetClient
{
    private readonly ILCEService _lceService;
    private readonly IMatrixService _matrixService;
    private readonly IParameterService _parameterService;
    private readonly ICheckListService _checkListService;
    private readonly IMissionService _missionService;
    private readonly ISampleService _sampleService;
    private readonly IAnswerService _answerService;
    
    public FoodNetClient(
        ILCEService lceService,
        IMatrixService matrixService,
        IParameterService parameterService,
        ICheckListService checkListService,
        IMissionService missionService,
        ISampleService sampleService,
        IAnswerService answerService)
    {
        _lceService = lceService;
        _matrixService = matrixService;
        _parameterService = parameterService;
        _checkListService = checkListService;
        _missionService = missionService;
        _sampleService = sampleService;
        _answerService = answerService;
    }

    #region Mission Operations
    public async Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId, CancellationToken cancellationToken = default)
    {
        return await _missionService.GetMissionsAsync(operatorId, cancellationToken);
    }

    public async Task<MissionDto> GetMissionByIdAsync(string missionId, CancellationToken cancellationToken = default)
    {
        return await _missionService.GetMissionByIdAsync(missionId, cancellationToken);
    }

    public async Task<byte[]> GenerateMissionPdfAsync(string missionId, int templateVersionId, CancellationToken cancellationToken = default)
    {
        return await _missionService.GenerateMissionPdfAsync(missionId, templateVersionId, cancellationToken);
    }
    #endregion

    #region Sample Operations
    
    public async Task<IEnumerable<SampleListDto>> GetSamplesAsync(int operatorId, CancellationToken cancellationToken = default)
    {
        return await _sampleService.GetSamplesAsync(operatorId, cancellationToken);
    }

    public async Task<IEnumerable<SampleDto>> GetSampleDetailsAsync(int operatorId, int controlId, CancellationToken cancellationToken = default)
    {
        return await _sampleService.GetSampleDetailsAsync(operatorId, controlId, cancellationToken);
    }

    public async Task<SampleDto> GetSampleByIdAsync(string sampleId, CancellationToken cancellationToken = default)
    {
        return await _sampleService.GetSampleByIdAsync(sampleId, cancellationToken);
    }
    
    #endregion

    #region LCE Operations
    public async Task<IEnumerable<LCEDto>> GetLCEsAsync(CancellationToken cancellationToken = default)
    {
        return await _lceService.GetLCEs(cancellationToken);
    }

    public async Task<LCEDto> GetLCEByServiceCodeAsync(string serviceCode, CancellationToken cancellationToken = default)
    {
        var lces = await _lceService.GetLCEs(cancellationToken);
        return lces.FirstOrDefault(lce => lce.ServiceCode == serviceCode);
    }
    #endregion

    #region Matrix Operations
    public async Task<IEnumerable<MatrixDto>> GetMatricesLevel4Async(CancellationToken cancellationToken = default)
    {
        return await _matrixService.GetMatrixLevel4Async(cancellationToken);
    }

    public async Task<IEnumerable<MatrixDto>> GetMatricesLevel5Async(CancellationToken cancellationToken = default)
    {
        return await _matrixService.GetMatrixLevel5Async(cancellationToken);
    }

    public async Task<IEnumerable<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken = default)
    {
        return await _matrixService.GetMatricesByLevelAsync(level, cancellationToken);
    }

    public async Task<MatrixDto> GetMatrixByIdAsync(string matrixId, CancellationToken cancellationToken = default)
    {
        return await _matrixService.GetMatrixByIdAsync(matrixId, cancellationToken);
    }
    #endregion

    #region Parameter Operations
    public async Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken = default)
    {
        return await _parameterService.GetParametersAsync(cancellationToken);
    }

    public async Task<ParameterDto> GetParameterByIdAsync(int parameterId, CancellationToken cancellationToken = default)
    {
        var parameters = await _parameterService.GetParametersAsync(cancellationToken);
        return parameters.FirstOrDefault(p => p.Id == parameterId);
    }
    #endregion

    #region Checklist Operations
    public async Task<IEnumerable<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken = default)
    {
        return await _checkListService.GetQuestionsAsync(templateVersionId, cancellationToken);
    }

    public async Task<IEnumerable<AnswerDto>> GetAnswersAsync(string missionId, CancellationToken cancellationToken = default)
    {
        return await _answerService.GetAnswersAsync(missionId, cancellationToken);
    }

    public async Task<IEnumerable<AnswerDto>> GetAnswersByTemplateAsync(int templateVersionId, CancellationToken cancellationToken = default)
    {
        return await _answerService.GetAnswersByTemplateAsync(templateVersionId, cancellationToken);
    }

    public async Task<AnswerDto> CreateAnswerAsync(AnswerDto AnswerDto, CancellationToken cancellationToken = default)
    {
        return await _answerService.CreateAnswerAsync(AnswerDto, cancellationToken);
    }

    public async Task<AnswerDto> UpdateAnswerAsync(int id, AnswerDto AnswerDto, CancellationToken cancellationToken = default)
    {
        return await _answerService.UpdateAnswerAsync(id, AnswerDto, cancellationToken);
    }

    public async Task DeleteAnswerAsync(int id, CancellationToken cancellationToken = default)
    {
        await _answerService.DeleteAnswerAsync(id, cancellationToken);
    }
    #endregion
}
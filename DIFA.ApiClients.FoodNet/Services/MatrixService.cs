using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IMatrixService
{
    Task<MatrixDto?> GetMatrixByIdAsync(string id, CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatrixLevel4Async(CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatrixLevel5Async(CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken);
}

public class MatrixService(AlphaContext alphaContext) : IMatrixService
{
    public async Task<MatrixDto?> GetMatrixByIdAsync(string id, CancellationToken cancellationToken)
    {
        var matrix = await alphaContext.Matrices
            .FirstOrDefaultAsync(x => x.MatrixId == id, cancellationToken);
        
        if(matrix != null)
            return new MatrixDto{
                MatrixId = id,
                DescriptionNL = matrix.DescriptionNL,
                DescriptionFR = matrix.DescriptionFR,
                Level = matrix.Level
            };

        return null;
    }
    
    public async Task<List<MatrixDto>> GetMatrixLevel4Async(CancellationToken cancellationToken)
    {
        return await GetMatricesByLevelAsync(4, cancellationToken);
    }
    
    public async Task<List<MatrixDto>> GetMatrixLevel5Async(CancellationToken cancellationToken)
    {
        return await GetMatricesByLevelAsync(5, cancellationToken);
    }
    
    public async Task<List<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken)
    {
        var matrices = await alphaContext.Matrices
            .Where(m => m.Level == level)
            .Select(m => new MatrixDto
            {
                MatrixId = m.MatrixId,
                DescriptionNL = m.DescriptionNL,
                DescriptionFR = m.DescriptionFR,
                Level = m.Level
            })
            .OrderBy(m => m.MatrixId)
            .ToListAsync(cancellationToken);
            
        return matrices;
    }
}
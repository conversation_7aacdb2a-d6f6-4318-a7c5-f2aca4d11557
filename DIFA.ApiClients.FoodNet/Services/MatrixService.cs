using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IMatrixService
{
    Task<MatrixDto?> GetMatrixByIdAsync(string id, CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatrixLevel4Async(CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatrixLevel5Async(CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken);
}

public class MatrixService(AlphaContext alphaContext) : IMatrixService
{
    public async Task<MatrixDto?> GetMatrixByIdAsync(string id, CancellationToken cancellationToken)
    {
        // Try Level 4 first
        var level4Matrices = await GetMatrixLevel4Async(cancellationToken);
        var matrix = level4Matrices.FirstOrDefault(m => m.MatrixId == id);

        if (matrix != null)
            return matrix;

        // Try Level 5 if not found in Level 4
        var level5Matrices = await GetMatrixLevel5Async(cancellationToken);
        matrix = level5Matrices.FirstOrDefault(m => m.MatrixId == id);

        return matrix;
    }
    
    public async Task<List<MatrixDto>> GetMatrixLevel4Async(CancellationToken cancellationToken)
    {
        // Based on MatrixLevel4.sql - query ALPHA.MATRIX_NIV_4 table
        var matrices = await alphaContext.Database
            .SqlQueryRaw<MatrixDto>(@"
                SELECT MATRIX_NIV_4_ID as MatrixId,
                       OMSCHRIJVING_NL as DescriptionNL,
                       OMSCHRIJVING_FR as DescriptionFR,
                       4 as Level
                FROM ALPHA.MATRIX_NIV_4")
            .ToListAsync(cancellationToken);

        return matrices;
    }

    public async Task<List<MatrixDto>> GetMatrixLevel5Async(CancellationToken cancellationToken)
    {
        // Based on MatrixLevel5.sql - query ALPHA.MATRIX_NIV_5 table
        var matrices = await alphaContext.Database
            .SqlQueryRaw<MatrixDto>(@"
                SELECT MATRIX_NIV_5_ID as MatrixId,
                       OMSCHRIJVING_NL as DescriptionNL,
                       OMSCHRIJVING_FR as DescriptionFR,
                       5 as Level
                FROM ALPHA.MATRIX_NIV_5")
            .ToListAsync(cancellationToken);

        return matrices;
    }

    public async Task<List<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken)
    {
        return level switch
        {
            4 => await GetMatrixLevel4Async(cancellationToken),
            5 => await GetMatrixLevel5Async(cancellationToken),
            _ => new List<MatrixDto>()
        };
    }
}
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IParameterService
{
    Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken);
}

public class ParameterService(AlphaContext alphaContext) : IParameterService
{
    public async Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken)
    {
        var parameters = await alphaContext.Parameters
            .Select(p => new ParameterDto
            {
                Id = p.Id,
                DescriptionNL = p.DescriptionNL,
                DescriptionFR = p.DescriptionFR
            })
            .ToListAsync(cancellationToken);

        return parameters;
    }
}
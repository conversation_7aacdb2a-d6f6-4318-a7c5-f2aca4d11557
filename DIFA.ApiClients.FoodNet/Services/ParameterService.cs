using System.Runtime.CompilerServices;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Helpers;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IParameterService
{
    Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken);
}

public class ParameterService : IParameterService
{
    private readonly AlphaContext _alphaContext;
    
    public ParameterService(AlphaContext alphaContext)
    {
        _alphaContext = alphaContext;
    }
    
    public async Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken)
    {
        // Based on Parameter.sql
        var parameters = await _alphaContext.Database
            .SqlQueryRaw<ParameterDto>(@"
                SELECT PARAMETER_ID as Id,
                       OMSCHRIJVING_NL as DescriptionNL,
                       OMSCHRIJVING_FR as DescriptionFR
                FROM ALPHA.PARAMETER")
            .ToListAsync(cancellationToken);

        return parameters;
    }
}
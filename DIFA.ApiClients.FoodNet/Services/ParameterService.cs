using System.Runtime.CompilerServices;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Helpers;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IParameterService
{
    Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken);
}

public class ParameterService : IParameterService
{
    private readonly AlphaContext _alphaContext;
    
    public ParameterService(AlphaContext alphaContext)
    {
        _alphaContext = alphaContext;
    }
    
    public async Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken)
    {
        // Based on Parameter.sql - using Entity Framework Core
        var parameters = await _alphaContext.Parameters
            .Select(p => new ParameterDto
            {
                Id = p.Id,
                DescriptionNL = p.DescriptionNL,
                DescriptionFR = p.DescriptionFR
            })
            .ToListAsync(cancellationToken);

        return parameters;
    }
}
using AutoMapper;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IMissionService
{
    Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId, CancellationToken cancellationToken = default);
    Task<MissionDto> GetMissionByIdAsync(string missionId, CancellationToken cancellationToken = default);

    Task<byte[]> GenerateMissionPdfAsync(string missionId, int templateVersionId,
        CancellationToken cancellationToken = default);
}

public class MissionService(FoodNetContext foodNetContext, AlphaContext alphaContext, IMapper mapper) : IMissionService
{
    public async Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId,
        CancellationToken cancellationToken = default)
    {
        var missions = await foodNetContext.Missions
            .Where(m => m.OperatorId == operatorId)
            .Select(m => new MissionDto
            {
                MissionId = m.MissionId.ToString(),
                MissionNb = m.MissionNb,
                PlannedStartDate = m.PlannedStartDateHour ?? DateTime.MinValue,
                PlannedEndDate = m.PlannedEndDateHour ?? DateTime.MinValue,
                ActualStartDate = m.ActualStartDateHour ?? DateTime.MinValue,
                ActualEndDate = m.ActualEndDateHour ?? DateTime.MinValue,
                LCE = "" //TODO: Map LCE from Mission entity
            })
            .ToListAsync(cancellationToken);

        // Get LCE details from the Alpha context
        var lceCodes = missions.Select(m => m.LCE).Distinct().ToList();
        var lces = await alphaContext.LCEs
            .Where(lce => lceCodes.Contains(lce.ServiceCode))
            .ToListAsync(cancellationToken);

        // Map LCE data
        foreach (var mission in missions)
        {
            var lce = lces.FirstOrDefault(lce => lce.ServiceCode == mission.LCE);

            if (lce != null)
                mission.LceDto = mapper.Map<LCEDto>(lce);
        }

        return missions;
    }

    public Task<MissionDto> GetMissionByIdAsync(string missionId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<byte[]> GenerateMissionPdfAsync(string missionId, int templateVersionId,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}
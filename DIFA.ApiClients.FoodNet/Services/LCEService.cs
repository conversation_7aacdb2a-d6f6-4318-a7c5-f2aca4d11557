using System.Reflection;
using System.Runtime.CompilerServices;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ILCEService
{
    Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken);
}

public class LCEService : ILCEService
{
    private readonly AlphaContext _alphaContext;
    
    public LCEService(AlphaContext alphaContext)
    {
        _alphaContext = alphaContext;
    }
    
    public async Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken)
    {
        var lces = await _alphaContext.LCEs
            .Select(lce => new LCEDto
            {
                ServiceCode = lce.ServiceCode,
                DescriptionNl = lce.DescriptionNl,
                DescriptionFr = lce.DescriptionFr
            })
            .ToListAsync(cancellationToken);
            
        return lces;
    }
}
using System.Reflection;
using System.Runtime.CompilerServices;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ILCEService
{
    Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken);
}

public class LCEService : ILCEService
{
    private readonly AlphaContext _alphaContext;
    
    public LCEService(AlphaContext alphaContext)
    {
        _alphaContext = alphaContext;
    }
    
    public async Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken)
    {
        // Based on LCE.sql - union of ALPHA.LCE and ALPHA.PCE tables
        var lces = await _alphaContext.Database
            .SqlQueryRaw<LCEDto>(@"
                SELECT DIENST_CODE as ServiceCode,
                       OMSCHRIJVING_NL as DescriptionNl,
                       OMSCHRIJVING_FR as DescriptionFr
                FROM ALPHA.LCE
                WHERE DIENST_CODE IS NOT NULL
                UNION
                SELECT DIENST_CODE as ServiceCode,
                       OMSCHRIJVING_NL as DescriptionNl,
                       OMSCHRIJVING_FR as DescriptionFr
                FROM ALPHA.PCE
                WHERE DIENST_CODE IS NOT NULL")
            .ToListAsync(cancellationToken);

        return lces;
    }
}
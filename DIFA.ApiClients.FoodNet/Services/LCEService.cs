using System.Reflection;
using System.Runtime.CompilerServices;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ILCEService
{
    Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken);
}

public class LCEService : ILCEService
{
    private readonly AlphaContext _alphaContext;
    
    public LCEService(AlphaContext alphaContext)
    {
        _alphaContext = alphaContext;
    }
    
    public async Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken)
    {
        // Based on LCE.sql - union of ALPHA.LCE and ALPHA.PCE tables using Entity Framework Core
        // Get from LCE table
        var lceResults = await _alphaContext.LCEs
            .Where(lce => lce.ServiceCode != null)
            .Select(lce => new LCEDto
            {
                ServiceCode = lce.ServiceCode,
                DescriptionNl = lce.DescriptionNl,
                DescriptionFr = lce.DescriptionFr
            })
            .ToListAsync(cancellationToken);

        // Get from PCE table
        var pceResults = await _alphaContext.PCEs
            .Where(pce => pce.ServiceCode != null)
            .Select(pce => new LCEDto
            {
                ServiceCode = pce.ServiceCode,
                DescriptionNl = pce.DescriptionNl,
                DescriptionFr = pce.DescriptionFr
            })
            .ToListAsync(cancellationToken);

        // Combine results (UNION equivalent)
        var combinedResults = lceResults
            .Union(pceResults, new LCEDtoComparer())
            .ToList();

        return combinedResults;
    }
}

/// <summary>
/// Comparer for LCEDto to handle UNION operation (removes duplicates based on ServiceCode)
/// </summary>
public class LCEDtoComparer : IEqualityComparer<LCEDto>
{
    public bool Equals(LCEDto x, LCEDto y)
    {
        if (x == null && y == null) return true;
        if (x == null || y == null) return false;
        return x.ServiceCode == y.ServiceCode;
    }

    public int GetHashCode(LCEDto obj)
    {
        return obj?.ServiceCode?.GetHashCode() ?? 0;
    }
}
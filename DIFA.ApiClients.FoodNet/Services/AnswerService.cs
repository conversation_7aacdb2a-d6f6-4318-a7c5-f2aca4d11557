using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IAnswerService
{
    Task<IEnumerable<AnswerDto>> GetAnswersAsync(string missionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnswerDto>> GetAnswersByTemplateAsync(int templateVersionId, CancellationToken cancellationToken = default);
    Task<AnswerDto> CreateAnswerAsync(AnswerDto AnswerDto, CancellationToken cancellationToken = default);
    Task<AnswerDto> UpdateAnswerAsync(int id, AnswerDto AnswerDto, CancellationToken cancellationToken = default);
    Task DeleteAnswerAsync(int id, CancellationToken cancellationToken = default);
}

public class AnswerService(DynamoContext dynamoContext) : IAnswerService
{
    public async Task<IEnumerable<AnswerDto>> GetAnswersAsync(string missionId, CancellationToken cancellationToken)
    {
        var answers = await dynamoContext.Answers
            .Where(a => a.MissionId == missionId)
            .Select(a => new AnswerDto
            {
                TemplateVersionId = a.TemplateVersionId,
                QuestionResultId = a.QuestionResultId,
                Result = a.Result,
                ScoreId = a.ScoreId,
                MissionId = a.MissionId
            })
            .ToListAsync(cancellationToken);
            
        return answers;
    }
    
    public async Task<IEnumerable<AnswerDto>> GetAnswersByTemplateAsync(int templateVersionId, CancellationToken cancellationToken)
    {
        var answers = await dynamoContext.Answers
            .Where(a => a.TemplateVersionId == templateVersionId)
            .Select(a => new AnswerDto
            {
                TemplateVersionId = a.TemplateVersionId,
                QuestionResultId = a.QuestionResultId,
                Result = a.Result,
                ScoreId = a.ScoreId,
                MissionId = a.MissionId
            })
            .ToListAsync(cancellationToken);
            
        return answers;
    }
    
    public async Task<AnswerDto> CreateAnswerAsync(AnswerDto AnswerDto, CancellationToken cancellationToken)
    {
        var answer = new Answer
        {
            TemplateVersionId = AnswerDto.TemplateVersionId,
            QuestionResultId = AnswerDto.QuestionResultId,
            Result = AnswerDto.Result,
            ScoreId = AnswerDto.ScoreId,
            MissionId = AnswerDto.MissionId
        };
        
        dynamoContext.Answers.Add(answer);
        await dynamoContext.SaveChangesAsync(cancellationToken);
        
        AnswerDto.TemplateVersionId = answer.TemplateVersionId;
        return AnswerDto;
    }
    
    public async Task<AnswerDto> UpdateAnswerAsync(int id, AnswerDto AnswerDto, CancellationToken cancellationToken)
    {
        var answer = await dynamoContext.Answers.FindAsync(id, cancellationToken);
        if (answer == null)
            throw new ArgumentException($"Answer with ID {id} not found");
            
        answer.Result = AnswerDto.Result;
        answer.ScoreId = AnswerDto.ScoreId;
        
        await dynamoContext.SaveChangesAsync(cancellationToken);
        return AnswerDto;
    }
    
    public async Task DeleteAnswerAsync(int id, CancellationToken cancellationToken)
    {
        var answer = await dynamoContext.Answers.FindAsync(id, cancellationToken);
        if (answer != null)
        {
            dynamoContext.Answers.Remove(answer);
            await dynamoContext.SaveChangesAsync(cancellationToken);
        }
    }
}
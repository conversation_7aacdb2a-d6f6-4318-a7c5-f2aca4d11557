using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ISampleService
{
    Task<IEnumerable<SampleListDto>> GetSamplesAsync(int operatorId, CancellationToken cancellationToken = default);
    Task<IEnumerable<SampleDto>> GetSampleDetailsAsync(int operatorId, int controlId, CancellationToken cancellationToken = default);
    Task<SampleDto?> GetSampleByIdAsync(string sampleId, CancellationToken cancellationToken = default);
}

public class SampleService : ISampleService
{
    private readonly FoodNetContext _foodNetContext;
    private readonly IParameterService _parameterService;
    private readonly IMatrixService _matrixService;
    
    public SampleService(
        FoodNetContext foodNetContext, 
        IParameterService parameterService,
        IMatrixService matrixService)
    {
        _foodNetContext = foodNetContext;
        _parameterService = parameterService;
        _matrixService = matrixService;
    }
    
    public async Task<IEnumerable<SampleListDto>> GetSamplesAsync(int operatorId, CancellationToken cancellationToken = default)
    {
        // Get samples using LINQ instead of embedded SQL
        var samples = await _foodNetContext.SampleLists
            .Where(s => s.OperatorId == operatorId)
            .Take(5) // Your original limit of 5
            .Select(s => new SampleListDto
            {
                SampleNumber = s.MONSTERNUMMER,
                ControlId = s.CONTROLE_ID,
                DateSample = s.DATUMMONSTERNAME,
                Result = s.RESULTAAT,
                MatrixNiv4Id = s.MATRIX_NIV_4_ID,
                MatrixNiv5Id = s.MATRIX_NIV_5_ID
            })
            .ToListAsync(cancellationToken);
        
        if (!samples.Any())
            return samples;
        
        // Get matrices for enrichment
        var matricesLevel4 = await _matrixService.GetMatrixLevel4Async(cancellationToken);
        var matricesLevel5 = await _matrixService.GetMatrixLevel5Async(cancellationToken);

        // Enrich samples with matrix information
        foreach (var sample in samples)
        {
            sample.MatrixNiv4 = matricesLevel4.SingleOrDefault(m => m.MatrixId == sample.MatrixNiv4Id);
            sample.MatrixNiv5 = matricesLevel5.SingleOrDefault(m => m.MatrixId == sample.MatrixNiv5Id);
        }
        
        return samples;
    }
    
    public async Task<IEnumerable<SampleDto>> GetSampleDetailsAsync(int operatorId, int controlId, CancellationToken cancellationToken = default)
    {
        // Get sample details using LINQ with proper joins
        var samples = await _foodNetContext.Samples
            .Include(s => s.Mission)
            .Where(s => s.Mission.OperatorId == operatorId && 
                       s.ControlId == controlId.ToString())
            .Select(s => new SampleDto
            {
                SampleParameterId = s.SampleParameterId,
                IsExtra = s.IsExtra,
                ParameterId = s.ParameterId,
                ParameterResult = s.ParameterResult,
                SampleNb = s.SampleNb,
                SampleId = s.SampleId,
                ControlId = s.ControlId,
                SamplingId = s.SamplingId,
                SampleResult = s.SampleResult
            })
            .ToListAsync(cancellationToken);

        if (!samples.Any())
            return samples;

        // Get parameter descriptions for enrichment
        var parameterDescriptions = await _parameterService.GetParametersAsync(cancellationToken);
        
        // Enrich samples with parameter information
        foreach (var sample in samples)
        {
            sample.Parameter = parameterDescriptions.FirstOrDefault(p => p.Id == sample.ParameterId);
        }
        
        return samples;
    }
    
    public async Task<SampleDto?> GetSampleByIdAsync(string sampleId, CancellationToken cancellationToken = default)
    {
        var sample = await _foodNetContext.Samples
            .Include(s => s.Mission)
            .Where(s => s.SampleId == sampleId)
            .Select(s => new SampleDto
            {
                SampleParameterId = s.SampleParameterId,
                IsExtra = s.IsExtra,
                ParameterId = s.ParameterId,
                ParameterResult = s.ParameterResult,
                SampleNb = s.SampleNb,
                SampleId = s.SampleId,
                ControlId = s.ControlId,
                SamplingId = s.SamplingId,
                SampleResult = s.SampleResult
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (sample == null)
            return null;

        // Get parameter description for enrichment
        var parameterDescriptions = await _parameterService.GetParametersAsync(cancellationToken);
        sample.Parameter = parameterDescriptions.FirstOrDefault(p => p.Id == sample.ParameterId);
        
        return sample;
    }
}
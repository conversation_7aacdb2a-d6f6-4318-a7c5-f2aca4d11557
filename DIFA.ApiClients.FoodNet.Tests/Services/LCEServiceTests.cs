using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class LCEServiceTests : TestBase
{
    [Fact]
    public async Task GetLCEs_WithLCEAndPCEData_ReturnsUnionOfBoth()
    {
        // Act - Using FoodNetClient which delegates to WireMock
        var result = await Client.GetLCEsAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(4); // Based on the JSON mock data: LCE001, LCE002, PCE001, PCE002

        result.Should().Contain(lce => lce.ServiceCode == "LCE001");
        result.Should().Contain(lce => lce.ServiceCode == "LCE002");
        result.Should().Contain(lce => lce.ServiceCode == "PCE001");
        result.Should().Contain(lce => lce.ServiceCode == "PCE002");

        // Verify descriptions
        var lce001 = result.First(lce => lce.ServiceCode == "LCE001");
        lce001.DescriptionNl.Should().Be("Laboratorium Controle Eenheid 1 NL");
        lce001.DescriptionFr.Should().Be("Unité de Contrôle Laboratoire 1 FR");
    }

    [Fact]
    public async Task GetLCEByServiceCodeAsync_WithValidServiceCode_ReturnsLCE()
    {
        // Act - Using FoodNetClient which delegates to WireMock
        var result = await Client.GetLCEByServiceCodeAsync("LCE001", CancellationToken.None);

        // Assert - For now, this will return null since we don't have individual LCE mappings
        // In a real implementation, you would add specific mappings for individual LCEs
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetLCEByServiceCodeAsync_WithNonExistentServiceCode_ReturnsNull()
    {
        // Act
        var result = await Client.GetLCEByServiceCodeAsync("NON_EXISTENT", CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }
}

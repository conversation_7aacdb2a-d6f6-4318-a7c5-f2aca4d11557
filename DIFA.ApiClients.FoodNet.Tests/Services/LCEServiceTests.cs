using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Entities;
using DIFA.ApiClients.FoodNet.Services;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class LCEServiceTests : IDisposable
{
    private readonly AlphaContext _alphaContext;
    private readonly LCEService _lceService;

    public LCEServiceTests()
    {
        var options = new DbContextOptionsBuilder<AlphaContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _alphaContext = new AlphaContext(options);
        _lceService = new LCEService(_alphaContext);
    }

    [Fact]
    public async Task GetLCEs_WithLCEAndPCEData_ReturnsUnionOfBoth()
    {
        // Arrange
        var lces = new List<LCE>
        {
            new LCE
            {
                ServiceCode = "LCE001",
                DescriptionNl = "LCE Description NL 1",
                DescriptionFr = "LCE Description FR 1"
            },
            new LCE
            {
                ServiceCode = "LCE002",
                DescriptionNl = "LCE Description NL 2",
                DescriptionFr = "LCE Description FR 2"
            },
            new LCE
            {
                ServiceCode = null, // This should be filtered out
                DescriptionNl = "LCE Description NL 3",
                DescriptionFr = "LCE Description FR 3"
            }
        };

        var pces = new List<PCE>
        {
            new PCE
            {
                ServiceCode = "PCE001",
                DescriptionNl = "PCE Description NL 1",
                DescriptionFr = "PCE Description FR 1"
            },
            new PCE
            {
                ServiceCode = "LCE001", // Duplicate with LCE - should be deduplicated
                DescriptionNl = "PCE Description NL 2",
                DescriptionFr = "PCE Description FR 2"
            },
            new PCE
            {
                ServiceCode = null, // This should be filtered out
                DescriptionNl = "PCE Description NL 3",
                DescriptionFr = "PCE Description FR 3"
            }
        };

        await _alphaContext.LCEs.AddRangeAsync(lces);
        await _alphaContext.PCEs.AddRangeAsync(pces);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _lceService.GetLCEs(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(3); // LCE001, LCE002, PCE001 (duplicates removed, nulls filtered)
        
        result.Should().Contain(lce => lce.ServiceCode == "LCE001");
        result.Should().Contain(lce => lce.ServiceCode == "LCE002");
        result.Should().Contain(lce => lce.ServiceCode == "PCE001");
        
        // Verify that null ServiceCodes are filtered out
        result.Should().NotContain(lce => lce.ServiceCode == null);
    }

    [Fact]
    public async Task GetLCEs_WithOnlyLCEData_ReturnsLCEData()
    {
        // Arrange
        var lces = new List<LCE>
        {
            new LCE
            {
                ServiceCode = "LCE001",
                DescriptionNl = "LCE Description NL 1",
                DescriptionFr = "LCE Description FR 1"
            },
            new LCE
            {
                ServiceCode = "LCE002",
                DescriptionNl = "LCE Description NL 2",
                DescriptionFr = "LCE Description FR 2"
            }
        };

        await _alphaContext.LCEs.AddRangeAsync(lces);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _lceService.GetLCEs(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var firstLce = result.First(lce => lce.ServiceCode == "LCE001");
        firstLce.DescriptionNl.Should().Be("LCE Description NL 1");
        firstLce.DescriptionFr.Should().Be("LCE Description FR 1");
    }

    [Fact]
    public async Task GetLCEs_WithOnlyPCEData_ReturnsPCEData()
    {
        // Arrange
        var pces = new List<PCE>
        {
            new PCE
            {
                ServiceCode = "PCE001",
                DescriptionNl = "PCE Description NL 1",
                DescriptionFr = "PCE Description FR 1"
            },
            new PCE
            {
                ServiceCode = "PCE002",
                DescriptionNl = "PCE Description NL 2",
                DescriptionFr = "PCE Description FR 2"
            }
        };

        await _alphaContext.PCEs.AddRangeAsync(pces);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _lceService.GetLCEs(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var firstPce = result.First(pce => pce.ServiceCode == "PCE001");
        firstPce.DescriptionNl.Should().Be("PCE Description NL 1");
        firstPce.DescriptionFr.Should().Be("PCE Description FR 1");
    }

    [Fact]
    public async Task GetLCEs_WithNoData_ReturnsEmptyList()
    {
        // Act
        var result = await _lceService.GetLCEs(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetLCEs_WithNullServiceCodes_FiltersOutNulls()
    {
        // Arrange
        var lces = new List<LCE>
        {
            new LCE
            {
                ServiceCode = null,
                DescriptionNl = "LCE Description NL 1",
                DescriptionFr = "LCE Description FR 1"
            }
        };

        var pces = new List<PCE>
        {
            new PCE
            {
                ServiceCode = null,
                DescriptionNl = "PCE Description NL 1",
                DescriptionFr = "PCE Description FR 1"
            }
        };

        await _alphaContext.LCEs.AddRangeAsync(lces);
        await _alphaContext.PCEs.AddRangeAsync(pces);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _lceService.GetLCEs(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    public void Dispose()
    {
        _alphaContext?.Dispose();
    }
}

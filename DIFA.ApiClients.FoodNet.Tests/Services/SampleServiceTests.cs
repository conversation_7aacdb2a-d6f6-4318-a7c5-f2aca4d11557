using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Entities;
using DIFA.ApiClients.FoodNet.Services;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class SampleServiceTests : IDisposable
{
    private readonly FoodNetContext _foodNetContext;
    private readonly Mock<IParameterService> _parameterServiceMock;
    private readonly Mock<IMatrixService> _matrixServiceMock;
    private readonly SampleService _sampleService;

    public SampleServiceTests()
    {
        var options = new DbContextOptionsBuilder<FoodNetContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _foodNetContext = new FoodNetContext(options);
        _parameterServiceMock = new Mock<IParameterService>();
        _matrixServiceMock = new Mock<IMatrixService>();
        
        _sampleService = new SampleService(_foodNetContext, _parameterServiceMock.Object, _matrixServiceMock.Object);
    }

    [Fact]
    public async Task GetSamplesAsync_WithValidOperatorId_ReturnsFilteredSamples()
    {
        // Arrange
        var operatorId = 2087535;
        var sampleLists = new List<SampleList>
        {
            new SampleList
            {
                MONSTERNUMMER = "SAMPLE001",
                CONTROLE_ID = "CTRL001",
                DATUMMONSTERNAME = DateTime.Now.AddDays(-1),
                RESULTAAT = "PASS",
                MATRIX_NIV_4_ID = "M4_001",
                MATRIX_NIV_5_ID = "M5_001",
                OperatorId = operatorId
            },
            new SampleList
            {
                MONSTERNUMMER = "SAMPLE002",
                CONTROLE_ID = "CTRL002",
                DATUMMONSTERNAME = DateTime.Now.AddDays(-2),
                RESULTAAT = "FAIL",
                MATRIX_NIV_4_ID = "M4_002",
                MATRIX_NIV_5_ID = "M5_002",
                OperatorId = operatorId
            },
            new SampleList
            {
                MONSTERNUMMER = "SAMPLE003",
                CONTROLE_ID = "CTRL003",
                DATUMMONSTERNAME = DateTime.Now.AddDays(-3),
                RESULTAAT = "PASS",
                MATRIX_NIV_4_ID = "M4_003",
                MATRIX_NIV_5_ID = "M5_003",
                OperatorId = 9999 // Different operator, should be filtered out
            }
        };

        await _foodNetContext.SampleLists.AddRangeAsync(sampleLists);
        await _foodNetContext.SaveChangesAsync();

        var matrixLevel4 = new List<MatrixDto>
        {
            new MatrixDto { MatrixId = "M4_001", DescriptionNL = "Matrix 4 NL 1", DescriptionFR = "Matrix 4 FR 1", Level = 4 },
            new MatrixDto { MatrixId = "M4_002", DescriptionNL = "Matrix 4 NL 2", DescriptionFR = "Matrix 4 FR 2", Level = 4 }
        };

        var matrixLevel5 = new List<MatrixDto>
        {
            new MatrixDto { MatrixId = "M5_001", DescriptionNL = "Matrix 5 NL 1", DescriptionFR = "Matrix 5 FR 1", Level = 5 },
            new MatrixDto { MatrixId = "M5_002", DescriptionNL = "Matrix 5 NL 2", DescriptionFR = "Matrix 5 FR 2", Level = 5 }
        };

        _matrixServiceMock.Setup(m => m.GetMatrixLevel4Async(It.IsAny<CancellationToken>()))
            .ReturnsAsync(matrixLevel4);
        
        _matrixServiceMock.Setup(m => m.GetMatrixLevel5Async(It.IsAny<CancellationToken>()))
            .ReturnsAsync(matrixLevel5);

        // Act
        var result = await _sampleService.GetSamplesAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2); // Only samples for the specified operator
        
        var firstSample = result.First(s => s.SampleNumber == "SAMPLE001");
        firstSample.ControlId.Should().Be("CTRL001");
        firstSample.Result.Should().Be("PASS");
        firstSample.MatrixNiv4.Should().NotBeNull();
        firstSample.MatrixNiv4.MatrixId.Should().Be("M4_001");
        firstSample.MatrixNiv5.Should().NotBeNull();
        firstSample.MatrixNiv5.MatrixId.Should().Be("M5_001");
    }

    [Fact]
    public async Task GetSampleDetailsAsync_WithValidParameters_ReturnsSampleDetails()
    {
        // Arrange
        var operatorId = 2087535;
        var controlId = 12345;
        
        var mission = new Mission
        {
            MissionId = 1673,
            OperatorId = operatorId,
            MissionNb = "TEST_MISSION"
        };

        var samples = new List<Sample>
        {
            new Sample
            {
                SampleId = "SAMPLE001",
                SampleParameterId = "PARAM001",
                SampleNb = "SN001",
                ControlId = controlId.ToString(),
                SamplingId = "SAMPLING001",
                ParameterId = 1,
                ParameterResult = "RESULT1",
                SampleResult = "PASS",
                IsExtra = false,
                MissionId = 1673,
                Mission = mission
            },
            new Sample
            {
                SampleId = "SAMPLE002",
                SampleParameterId = "PARAM002",
                SampleNb = "SN002",
                ControlId = controlId.ToString(),
                SamplingId = "SAMPLING002",
                ParameterId = 2,
                ParameterResult = "RESULT2",
                SampleResult = "FAIL",
                IsExtra = true,
                MissionId = 1673,
                Mission = mission
            }
        };

        await _foodNetContext.Missions.AddAsync(mission);
        await _foodNetContext.Samples.AddRangeAsync(samples);
        await _foodNetContext.SaveChangesAsync();

        var parameters = new List<ParameterDto>
        {
            new ParameterDto { Id = 1, DescriptionNL = "Parameter 1 NL", DescriptionFR = "Parameter 1 FR" },
            new ParameterDto { Id = 2, DescriptionNL = "Parameter 2 NL", DescriptionFR = "Parameter 2 FR" }
        };

        _parameterServiceMock.Setup(p => p.GetParametersAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(parameters);

        // Act
        var result = await _sampleService.GetSampleDetailsAsync(operatorId, controlId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var firstSample = result.First(s => s.SampleId == "SAMPLE001");
        firstSample.ParameterId.Should().Be(1);
        firstSample.Parameter.Should().NotBeNull();
        firstSample.Parameter.DescriptionNL.Should().Be("Parameter 1 NL");
        firstSample.IsExtra.Should().BeFalse();
        
        var secondSample = result.First(s => s.SampleId == "SAMPLE002");
        secondSample.ParameterId.Should().Be(2);
        secondSample.Parameter.Should().NotBeNull();
        secondSample.Parameter.DescriptionNL.Should().Be("Parameter 2 NL");
        secondSample.IsExtra.Should().BeTrue();
    }

    [Fact]
    public async Task GetSampleByIdAsync_WithValidSampleId_ReturnsSample()
    {
        // Arrange
        var sampleId = "SAMPLE001";
        
        var mission = new Mission
        {
            MissionId = 1673,
            OperatorId = 2087535,
            MissionNb = "TEST_MISSION"
        };

        var sample = new Sample
        {
            SampleId = sampleId,
            SampleParameterId = "PARAM001",
            SampleNb = "SN001",
            ControlId = "12345",
            SamplingId = "SAMPLING001",
            ParameterId = 1,
            ParameterResult = "RESULT1",
            SampleResult = "PASS",
            IsExtra = false,
            MissionId = 1673,
            Mission = mission
        };

        await _foodNetContext.Missions.AddAsync(mission);
        await _foodNetContext.Samples.AddAsync(sample);
        await _foodNetContext.SaveChangesAsync();

        var parameters = new List<ParameterDto>
        {
            new ParameterDto { Id = 1, DescriptionNL = "Parameter 1 NL", DescriptionFR = "Parameter 1 FR" }
        };

        _parameterServiceMock.Setup(p => p.GetParametersAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(parameters);

        // Act
        var result = await _sampleService.GetSampleByIdAsync(sampleId);

        // Assert
        result.Should().NotBeNull();
        result.SampleId.Should().Be(sampleId);
        result.ParameterId.Should().Be(1);
        result.Parameter.Should().NotBeNull();
        result.Parameter.DescriptionNL.Should().Be("Parameter 1 NL");
    }

    [Fact]
    public async Task GetSampleByIdAsync_WithNonExistentSampleId_ReturnsNull()
    {
        // Act
        var result = await _sampleService.GetSampleByIdAsync("NON_EXISTENT");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetSamplesAsync_WithNoMatchingSamples_ReturnsEmptyList()
    {
        // Arrange
        var operatorId = 9999;

        _matrixServiceMock.Setup(m => m.GetMatrixLevel4Async(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MatrixDto>());
        
        _matrixServiceMock.Setup(m => m.GetMatrixLevel5Async(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MatrixDto>());

        // Act
        var result = await _sampleService.GetSamplesAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    public void Dispose()
    {
        _foodNetContext?.Dispose();
    }
}

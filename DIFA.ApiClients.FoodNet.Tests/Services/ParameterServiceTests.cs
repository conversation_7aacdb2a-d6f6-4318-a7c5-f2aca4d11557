using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Entities;
using DIFA.ApiClients.FoodNet.Services;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class ParameterServiceTests : IDisposable
{
    private readonly AlphaContext _alphaContext;
    private readonly ParameterService _parameterService;

    public ParameterServiceTests()
    {
        var options = new DbContextOptionsBuilder<AlphaContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _alphaContext = new AlphaContext(options);
        _parameterService = new ParameterService(_alphaContext);
    }

    [Fact]
    public async Task GetParametersAsync_WithParameters_ReturnsAllParameters()
    {
        // Arrange
        var parameters = new List<Parameter>
        {
            new Parameter
            {
                Id = 1,
                DescriptionNL = "Parameter NL 1",
                DescriptionFR = "Parameter FR 1"
            },
            new Parameter
            {
                Id = 2,
                DescriptionNL = "Parameter NL 2",
                DescriptionFR = "Parameter FR 2"
            },
            new Parameter
            {
                Id = 3,
                DescriptionNL = "Parameter NL 3",
                DescriptionFR = "Parameter FR 3"
            }
        };

        await _alphaContext.Parameters.AddRangeAsync(parameters);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _parameterService.GetParametersAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(3);
        
        var firstParameter = result.First(p => p.Id == 1);
        firstParameter.DescriptionNL.Should().Be("Parameter NL 1");
        firstParameter.DescriptionFR.Should().Be("Parameter FR 1");
        
        var secondParameter = result.First(p => p.Id == 2);
        secondParameter.DescriptionNL.Should().Be("Parameter NL 2");
        secondParameter.DescriptionFR.Should().Be("Parameter FR 2");
        
        var thirdParameter = result.First(p => p.Id == 3);
        thirdParameter.DescriptionNL.Should().Be("Parameter NL 3");
        thirdParameter.DescriptionFR.Should().Be("Parameter FR 3");
    }

    [Fact]
    public async Task GetParametersAsync_WithNoParameters_ReturnsEmptyList()
    {
        // Act
        var result = await _parameterService.GetParametersAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetParametersAsync_WithSingleParameter_ReturnsSingleParameter()
    {
        // Arrange
        var parameter = new Parameter
        {
            Id = 100,
            DescriptionNL = "Single Parameter NL",
            DescriptionFR = "Single Parameter FR"
        };

        await _alphaContext.Parameters.AddAsync(parameter);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _parameterService.GetParametersAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        
        var returnedParameter = result.First();
        returnedParameter.Id.Should().Be(100);
        returnedParameter.DescriptionNL.Should().Be("Single Parameter NL");
        returnedParameter.DescriptionFR.Should().Be("Single Parameter FR");
    }

    [Fact]
    public async Task GetParametersAsync_WithNullDescriptions_ReturnsParametersWithNullDescriptions()
    {
        // Arrange
        var parameter = new Parameter
        {
            Id = 200,
            DescriptionNL = null,
            DescriptionFR = null
        };

        await _alphaContext.Parameters.AddAsync(parameter);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _parameterService.GetParametersAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        
        var returnedParameter = result.First();
        returnedParameter.Id.Should().Be(200);
        returnedParameter.DescriptionNL.Should().BeNull();
        returnedParameter.DescriptionFR.Should().BeNull();
    }

    [Fact]
    public async Task GetParametersAsync_WithEmptyDescriptions_ReturnsParametersWithEmptyDescriptions()
    {
        // Arrange
        var parameter = new Parameter
        {
            Id = 300,
            DescriptionNL = "",
            DescriptionFR = ""
        };

        await _alphaContext.Parameters.AddAsync(parameter);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _parameterService.GetParametersAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        
        var returnedParameter = result.First();
        returnedParameter.Id.Should().Be(300);
        returnedParameter.DescriptionNL.Should().Be("");
        returnedParameter.DescriptionFR.Should().Be("");
    }

    public void Dispose()
    {
        _alphaContext?.Dispose();
    }
}

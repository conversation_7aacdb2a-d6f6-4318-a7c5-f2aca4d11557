using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class ParameterTests : TestBase
{
    [Fact]
    public async Task GetParametersAsync_WithParameters_ReturnsAllParameters()
    {
        // Act
        var result = await Client.GetParametersAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(5); // Based on the JSON mock data

        var salmonellaParameter = result.First(p => p.Id == 1);
        salmonellaParameter.DescriptionNL.Should().Be("Salmonella spp.");
        salmonellaParameter.DescriptionFR.Should().Be("Salmonella spp.");

        var listeriaParameter = result.First(p => p.Id == 2);
        listeriaParameter.DescriptionNL.Should().Be("Listeria monocytogenes");
        listeriaParameter.DescriptionFR.Should().Be("Listeria monocytogenes");

        var ecoliParameter = result.First(p => p.Id == 3);
        ecoliParameter.DescriptionNL.Should().Be("Escherichia coli");
        ecoliParameter.DescriptionFR.Should().Be("Escherichia coli");
    }
}

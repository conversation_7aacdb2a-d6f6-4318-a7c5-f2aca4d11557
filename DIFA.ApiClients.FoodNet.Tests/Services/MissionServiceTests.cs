using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class MissionServiceTests : TestBase
{
    [Fact]
    public async Task GetMissionsAsync_WithValidOperatorId_ReturnsFilteredMissions()
    {
        // Arrange
        var operatorId = 2087535;

        // Act - Using FoodNetClient which delegates to WireMock
        var result = await Client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2); // Based on the JSON mock data
        
        var missions = result.ToList();
        var firstMission = missions.First(m => m.MissionId == 1673);
        firstMission.MissionNb.Should().Be("2462/21/083243");
        firstMission.LCE.Should().Be("LCE001");
        firstMission.LceDto.Should().NotBeNull();
        firstMission.LceDto!.ServiceCode.Should().Be("LCE001");
        firstMission.LceDto.DescriptionNl.Should().Be("Laboratorium Controle Eenheid 1 NL");
        
        var secondMission = missions.First(m => m.MissionId == 1674);
        secondMission.MissionNb.Should().Be("2462/21/083244");
        secondMission.LCE.Should().Be("LCE002");
        secondMission.LceDto.Should().NotBeNull();
        secondMission.LceDto!.ServiceCode.Should().Be("LCE002");
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithValidMissionId_ReturnsMission()
    {
        // Arrange
        var missionId = "1673";

        // Act - Using FoodNetClient which delegates to WireMock
        var result = await Client.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().NotBeNull();
        result!.MissionId.Should().Be(1673);
        result.MissionNb.Should().Be("2462/21/083243");
        result.LCE.Should().Be("LCE001");
        result.LceDto.Should().NotBeNull();
        result.LceDto!.ServiceCode.Should().Be("LCE001");
        result.LceDto.DescriptionNl.Should().Be("Laboratorium Controle Eenheid 1 NL");
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithNonExistentMissionId_ReturnsNull()
    {
        // Arrange
        var missionId = "9999"; // This ID should trigger the mission-not-found.json mapping

        // Act
        var result = await Client.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetMissionsAsync_WithNoMatchingMissions_ReturnsEmptyList()
    {
        // Arrange
        var operatorId = 9999; // This should trigger the missions-empty-operator.json mapping

        // Act
        var result = await Client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }
}

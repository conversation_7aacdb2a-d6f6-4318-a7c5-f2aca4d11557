using AutoMapper;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Entities;
using DIFA.ApiClients.FoodNet.Services;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class MissionServiceTests : IDisposable
{
    private readonly FoodNetContext _foodNetContext;
    private readonly AlphaContext _alphaContext;
    private readonly Mock<IMapper> _mapperMock;
    private readonly MissionService _missionService;

    public MissionServiceTests()
    {
        // Setup in-memory databases
        var foodNetOptions = new DbContextOptionsBuilder<FoodNetContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        
        var alphaOptions = new DbContextOptionsBuilder<AlphaContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _foodNetContext = new FoodNetContext(foodNetOptions);
        _alphaContext = new AlphaContext(alphaOptions);
        _mapperMock = new Mock<IMapper>();
        
        _missionService = new MissionService(_foodNetContext, _alphaContext, _mapperMock.Object);
    }

    [Fact]
    public async Task GetMissionsAsync_WithValidOperatorId_ReturnsFilteredMissions()
    {
        // Arrange
        var operatorId = 2087535;
        var missions = new List<Mission>
        {
            new Mission
            {
                MissionId = 1673,
                MissionNb = "2462/21/083243",
                OperatorId = operatorId,
                Noe = 0,
                PlannedStartDateHour = DateTime.Now.AddDays(-1),
                PlannedEndDateHour = DateTime.Now,
                ActualStartDateHour = DateTime.Now.AddDays(-1),
                ActualEndDateHour = DateTime.Now,
                PceCodeOperator = "LCE001"
            },
            new Mission
            {
                MissionId = 1674,
                MissionNb = "2462/21/083244",
                OperatorId = operatorId,
                Noe = 1, // This should be filtered out
                PlannedStartDateHour = DateTime.Now.AddDays(-2),
                PlannedEndDateHour = DateTime.Now.AddDays(-1),
                ActualStartDateHour = DateTime.Now.AddDays(-2),
                ActualEndDateHour = DateTime.Now.AddDays(-1),
                PceCodeOperator = "LCE002"
            },
            new Mission
            {
                MissionId = 1675,
                MissionNb = "2462/21/083245",
                OperatorId = 9999, // Different operator, should be filtered out
                Noe = 0,
                PlannedStartDateHour = DateTime.Now.AddDays(-3),
                PlannedEndDateHour = DateTime.Now.AddDays(-2),
                ActualStartDateHour = DateTime.Now.AddDays(-3),
                ActualEndDateHour = DateTime.Now.AddDays(-2),
                PceCodeOperator = "LCE003"
            }
        };

        await _foodNetContext.Missions.AddRangeAsync(missions);
        await _foodNetContext.SaveChangesAsync();

        var lce = new LCE
        {
            ServiceCode = "LCE001",
            DescriptionNl = "Test LCE NL",
            DescriptionFr = "Test LCE FR"
        };
        
        await _alphaContext.LCEs.AddAsync(lce);
        await _alphaContext.SaveChangesAsync();

        var lceDto = new LCEDto
        {
            ServiceCode = "LCE001",
            DescriptionNl = "Test LCE NL",
            DescriptionFr = "Test LCE FR"
        };

        _mapperMock.Setup(m => m.Map<LCEDto>(It.IsAny<LCE>()))
            .Returns(lceDto);

        // Act
        var result = await _missionService.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1); // Only one mission should match (NOE = 0 and correct operator)
        
        var mission = result.First();
        mission.MissionId.Should().Be(1673);
        mission.MissionNb.Should().Be("2462/21/083243");
        mission.LCE.Should().Be("LCE001");
        mission.LceDto.Should().NotBeNull();
        mission.LceDto.ServiceCode.Should().Be("LCE001");
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithValidMissionId_ReturnsMission()
    {
        // Arrange
        var missionId = "1673";
        var mission = new Mission
        {
            MissionId = 1673,
            MissionNb = "2462/21/083243",
            OperatorId = 2087535,
            Noe = 0,
            PlannedStartDateHour = DateTime.Now.AddDays(-1),
            PlannedEndDateHour = DateTime.Now,
            ActualStartDateHour = DateTime.Now.AddDays(-1),
            ActualEndDateHour = DateTime.Now,
            PceCodeOperator = "LCE001"
        };

        await _foodNetContext.Missions.AddAsync(mission);
        await _foodNetContext.SaveChangesAsync();

        var lce = new LCE
        {
            ServiceCode = "LCE001",
            DescriptionNl = "Test LCE NL",
            DescriptionFr = "Test LCE FR"
        };
        
        await _alphaContext.LCEs.AddAsync(lce);
        await _alphaContext.SaveChangesAsync();

        var lceDto = new LCEDto
        {
            ServiceCode = "LCE001",
            DescriptionNl = "Test LCE NL",
            DescriptionFr = "Test LCE FR"
        };

        _mapperMock.Setup(m => m.Map<LCEDto>(It.IsAny<LCE>()))
            .Returns(lceDto);

        // Act
        var result = await _missionService.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().NotBeNull();
        result.MissionId.Should().Be(1673);
        result.MissionNb.Should().Be("2462/21/083243");
        result.LCE.Should().Be("LCE001");
        result.LceDto.Should().NotBeNull();
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithInvalidMissionId_ReturnsNull()
    {
        // Arrange
        var missionId = "invalid";

        // Act
        var result = await _missionService.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithNonExistentMissionId_ReturnsNull()
    {
        // Arrange
        var missionId = "9999";

        // Act
        var result = await _missionService.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetMissionsAsync_WithNoMatchingMissions_ReturnsEmptyList()
    {
        // Arrange
        var operatorId = 9999;

        // Act
        var result = await _missionService.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    public void Dispose()
    {
        _foodNetContext?.Dispose();
        _alphaContext?.Dispose();
    }
}

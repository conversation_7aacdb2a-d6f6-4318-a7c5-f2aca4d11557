using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class MatrixServiceTests : TestBase
{
    [Fact]
    public async Task GetMatrixLevel4Async_ReturnsLevel4Matrices()
    {
        // Act - Using WireMock data from matrices-level4-get-all.json
        var result = await MatrixHttpService.GetMatrixLevel4Async(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(4); // Based on the JSON mock data
        result.All(m => m.Level == 4).Should().BeTrue();

        var firstMatrix = result.First(m => m.MatrixId == "M4_001");
        firstMatrix.DescriptionNL.Should().Be("Matrix Level 4 Test 1 NL");
        firstMatrix.DescriptionFR.Should().Be("Matrix Level 4 Test 1 FR");

        // Verify realistic data
        var meatMatrix = result.First(m => m.MatrixId == "M4_003");
        meatMatrix.DescriptionNL.Should().Be("Vlees en vleesproducten");
        meatMatrix.DescriptionFR.Should().Be("Viande et produits carnés");
    }

    [Fact]
    public async Task GetMatrixLevel5Async_ReturnsLevel5Matrices()
    {
        // Act - Using WireMock data from matrices-level5-get-all.json
        var result = await MatrixHttpService.GetMatrixLevel5Async(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(4); // Based on the JSON mock data
        result.All(m => m.Level == 5).Should().BeTrue();

        var firstMatrix = result.First(m => m.MatrixId == "M5_001");
        firstMatrix.DescriptionNL.Should().Be("Matrix Level 5 Test 1 NL");
        firstMatrix.DescriptionFR.Should().Be("Matrix Level 5 Test 1 FR");

        // Verify realistic data
        var beefMatrix = result.First(m => m.MatrixId == "M5_003");
        beefMatrix.DescriptionNL.Should().Be("Rundvlees vers");
        beefMatrix.DescriptionFR.Should().Be("Bœuf frais");
    }

    [Fact]
    public async Task GetMatrixByIdAsync_WithLevel4Id_ReturnsLevel4Matrix()
    {
        // Act - This will use the first available Level 4 matrix from the mock data
        var result = await MatrixHttpService.GetMatrixByIdAsync("M4_001", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result!.MatrixId.Should().Be("M4_001");
        result.Level.Should().Be(4);
        result.DescriptionNL.Should().Be("Matrix Level 4 Test 1 NL");
        result.DescriptionFR.Should().Be("Matrix Level 4 Test 1 FR");
    }

    [Fact]
    public async Task GetMatrixByIdAsync_WithLevel5Id_ReturnsLevel5Matrix()
    {
        // Act - This will use the first available Level 5 matrix from the mock data
        var result = await MatrixHttpService.GetMatrixByIdAsync("M5_001", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result!.MatrixId.Should().Be("M5_001");
        result.Level.Should().Be(5);
        result.DescriptionNL.Should().Be("Matrix Level 5 Test 1 NL");
        result.DescriptionFR.Should().Be("Matrix Level 5 Test 1 FR");
    }

    [Fact]
    public async Task GetMatrixByIdAsync_WithNonExistentId_ReturnsNull()
    {
        // Act - Non-existent ID should return null
        var result = await MatrixHttpService.GetMatrixByIdAsync("NON_EXISTENT", CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }
}

using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Entities;
using DIFA.ApiClients.FoodNet.Services;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class MatrixServiceTests : IDisposable
{
    private readonly AlphaContext _alphaContext;
    private readonly MatrixService _matrixService;

    public MatrixServiceTests()
    {
        var options = new DbContextOptionsBuilder<AlphaContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _alphaContext = new AlphaContext(options);
        _matrixService = new MatrixService(_alphaContext);
    }

    [Fact]
    public async Task GetMatrixLevel4Async_ReturnsLevel4Matrices()
    {
        // Arrange
        var matrices = new List<MatrixLevel4>
        {
            new MatrixLevel4
            {
                MatrixNiv4Id = "M4_001",
                DescriptionNL = "Matrix Level 4 NL 1",
                DescriptionFR = "Matrix Level 4 FR 1"
            },
            new MatrixLevel4
            {
                MatrixNiv4Id = "M4_002",
                DescriptionNL = "Matrix Level 4 NL 2",
                DescriptionFR = "Matrix Level 4 FR 2"
            }
        };

        await _alphaContext.MatrixLevel4s.AddRangeAsync(matrices);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _matrixService.GetMatrixLevel4Async(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.All(m => m.Level == 4).Should().BeTrue();
        
        var firstMatrix = result.First(m => m.MatrixId == "M4_001");
        firstMatrix.DescriptionNL.Should().Be("Matrix Level 4 NL 1");
        firstMatrix.DescriptionFR.Should().Be("Matrix Level 4 FR 1");
    }

    [Fact]
    public async Task GetMatrixLevel5Async_ReturnsLevel5Matrices()
    {
        // Arrange
        var matrices = new List<MatrixLevel5>
        {
            new MatrixLevel5
            {
                MatrixNiv5Id = "M5_001",
                DescriptionNL = "Matrix Level 5 NL 1",
                DescriptionFR = "Matrix Level 5 FR 1"
            },
            new MatrixLevel5
            {
                MatrixNiv5Id = "M5_002",
                DescriptionNL = "Matrix Level 5 NL 2",
                DescriptionFR = "Matrix Level 5 FR 2"
            }
        };

        await _alphaContext.MatrixLevel5s.AddRangeAsync(matrices);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _matrixService.GetMatrixLevel5Async(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.All(m => m.Level == 5).Should().BeTrue();
        
        var firstMatrix = result.First(m => m.MatrixId == "M5_001");
        firstMatrix.DescriptionNL.Should().Be("Matrix Level 5 NL 1");
        firstMatrix.DescriptionFR.Should().Be("Matrix Level 5 FR 1");
    }

    [Fact]
    public async Task GetMatrixByIdAsync_WithLevel4Id_ReturnsLevel4Matrix()
    {
        // Arrange
        var matrix = new MatrixLevel4
        {
            MatrixNiv4Id = "M4_001",
            DescriptionNL = "Matrix Level 4 NL",
            DescriptionFR = "Matrix Level 4 FR"
        };

        await _alphaContext.MatrixLevel4s.AddAsync(matrix);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _matrixService.GetMatrixByIdAsync("M4_001", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MatrixId.Should().Be("M4_001");
        result.Level.Should().Be(4);
        result.DescriptionNL.Should().Be("Matrix Level 4 NL");
        result.DescriptionFR.Should().Be("Matrix Level 4 FR");
    }

    [Fact]
    public async Task GetMatrixByIdAsync_WithLevel5Id_ReturnsLevel5Matrix()
    {
        // Arrange
        var matrix = new MatrixLevel5
        {
            MatrixNiv5Id = "M5_001",
            DescriptionNL = "Matrix Level 5 NL",
            DescriptionFR = "Matrix Level 5 FR"
        };

        await _alphaContext.MatrixLevel5s.AddAsync(matrix);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _matrixService.GetMatrixByIdAsync("M5_001", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MatrixId.Should().Be("M5_001");
        result.Level.Should().Be(5);
        result.DescriptionNL.Should().Be("Matrix Level 5 NL");
        result.DescriptionFR.Should().Be("Matrix Level 5 FR");
    }

    [Fact]
    public async Task GetMatrixByIdAsync_WithNonExistentId_ReturnsNull()
    {
        // Act
        var result = await _matrixService.GetMatrixByIdAsync("NON_EXISTENT", CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetMatricesByLevelAsync_WithLevel4_ReturnsLevel4Matrices()
    {
        // Arrange
        var matrix = new MatrixLevel4
        {
            MatrixNiv4Id = "M4_001",
            DescriptionNL = "Matrix Level 4 NL",
            DescriptionFR = "Matrix Level 4 FR"
        };

        await _alphaContext.MatrixLevel4s.AddAsync(matrix);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _matrixService.GetMatricesByLevelAsync(4, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result.First().Level.Should().Be(4);
    }

    [Fact]
    public async Task GetMatricesByLevelAsync_WithLevel5_ReturnsLevel5Matrices()
    {
        // Arrange
        var matrix = new MatrixLevel5
        {
            MatrixNiv5Id = "M5_001",
            DescriptionNL = "Matrix Level 5 NL",
            DescriptionFR = "Matrix Level 5 FR"
        };

        await _alphaContext.MatrixLevel5s.AddAsync(matrix);
        await _alphaContext.SaveChangesAsync();

        // Act
        var result = await _matrixService.GetMatricesByLevelAsync(5, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result.First().Level.Should().Be(5);
    }

    [Fact]
    public async Task GetMatricesByLevelAsync_WithInvalidLevel_ReturnsEmptyList()
    {
        // Act
        var result = await _matrixService.GetMatricesByLevelAsync(99, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    public void Dispose()
    {
        _alphaContext?.Dispose();
    }
}

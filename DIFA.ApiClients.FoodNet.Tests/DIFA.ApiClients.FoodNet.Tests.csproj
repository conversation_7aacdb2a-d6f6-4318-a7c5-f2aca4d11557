<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
        <PackageReference Include="xunit" Version="2.6.1" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Moq" Version="4.20.69" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
        <PackageReference Include="AutoMapper" Version="12.0.1" />
        <PackageReference Include="FluentAssertions" Version="6.12.0" />
        <PackageReference Include="Azure.Identity" Version="1.10.4" />
        <PackageReference Include="MartinCostello.Logging.XUnit" Version="0.3.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet\DIFA.ApiClients.FoodNet.csproj" />
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet.Contract\DIFA.ApiClients.FoodNet.Contract.csproj" />
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet.Entities\DIFA.ApiClients.FoodNet.Entities.csproj" />
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet.Mock\DIFA.ApiClients.FoodNet.Mock.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Include="IntegrationTestSetup\appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>

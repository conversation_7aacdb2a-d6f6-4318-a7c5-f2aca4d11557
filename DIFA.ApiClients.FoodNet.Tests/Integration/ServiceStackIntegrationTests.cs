using DIFA.ApiClients.FoodNet.Services;
using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Integration;

[Trait("Category", "Integration")]
public class ServiceStackIntegrationTests : IntegrationTestBase
{
    private readonly IlCEService _ilCeService;
    private readonly MatrixService _matrixService;
    private readonly ParameterService _parameterService;
    private readonly SampleService _sampleService;
    private readonly CheckListService _checkListService;
    private readonly AnswerService _answerService;

    public ServiceStackIntegrationTests()
    {
        _ilCeService = new IlCEService(AlphaContext);
        _matrixService = new MatrixService(AlphaContext);
        _parameterService = new ParameterService(AlphaContext);
        _sampleService = new SampleService(FoodNetContext, _parameterService, _matrixService);
        _checkListService = new CheckListService(DynamoContext);
        _answerService = new AnswerService(DynamoContext);
    }

    [Fact]
    public async Task LCEService_GetLCEs_ReturnsUnionOfLCEAndPCE()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var result = await _ilCeService.GetLCEs(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(4); // 2 LCEs + 2 PCEs
        
        result.Should().Contain(lce => lce.ServiceCode == "LCE001");
        result.Should().Contain(lce => lce.ServiceCode == "LCE002");
        result.Should().Contain(lce => lce.ServiceCode == "PCE001");
        result.Should().Contain(lce => lce.ServiceCode == "PCE002");
    }

    [Fact]
    public async Task MatrixService_GetMatrixLevel4AndLevel5_ReturnsSeparateLevels()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var level4Results = await _matrixService.GetMatrixLevel4Async(CancellationToken.None);
        var level5Results = await _matrixService.GetMatrixLevel5Async(CancellationToken.None);

        // Assert
        level4Results.Should().NotBeNull();
        level4Results.Should().HaveCount(2);
        level4Results.Should().OnlyContain(m => m.Level == 4);
        level4Results.Should().Contain(m => m.MatrixId == "M4_001");
        level4Results.Should().Contain(m => m.MatrixId == "M4_002");

        level5Results.Should().NotBeNull();
        level5Results.Should().HaveCount(2);
        level5Results.Should().OnlyContain(m => m.Level == 5);
        level5Results.Should().Contain(m => m.MatrixId == "M5_001");
        level5Results.Should().Contain(m => m.MatrixId == "M5_002");
    }

    [Fact]
    public async Task ParameterService_GetParameters_ReturnsAllParameters()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var result = await _parameterService.GetParametersAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var parameters = result.ToList();
        parameters.Should().Contain(p => p.Id == 1 && p.DescriptionNL == "Parameter 1 NL");
        parameters.Should().Contain(p => p.Id == 2 && p.DescriptionNL == "Parameter 2 NL");
    }

    [Fact]
    public async Task SampleService_GetSamples_ReturnsEnrichedSamples()
    {
        // Arrange
        await SeedTestDataAsync();
        var operatorId = 2087535;

        // Act
        var result = await _sampleService.GetSamplesAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var samples = result.ToList();
        
        var firstSample = samples.First(s => s.SampleNumber == "SAMPLE001");
        firstSample.MatrixNiv4.Should().NotBeNull();
        firstSample.MatrixNiv4.MatrixId.Should().Be("M4_001");
        firstSample.MatrixNiv4.Level.Should().Be(4);
        firstSample.MatrixNiv5.Should().NotBeNull();
        firstSample.MatrixNiv5.MatrixId.Should().Be("M5_001");
        firstSample.MatrixNiv5.Level.Should().Be(5);
    }

    [Fact]
    public async Task SampleService_GetSampleDetails_ReturnsEnrichedSampleDetails()
    {
        // Arrange
        await SeedTestDataAsync();
        var operatorId = 2087535;
        var controlId = 12345;

        // Act
        var result = await _sampleService.GetSampleDetailsAsync(operatorId, controlId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var samples = result.ToList();
        
        var firstSample = samples.First(s => s.SampleId == "SAMPLE001");
        firstSample.Parameter.Should().NotBeNull();
        firstSample.Parameter.Id.Should().Be(1);
        firstSample.Parameter.DescriptionNL.Should().Be("Parameter 1 NL");
        
        var secondSample = samples.First(s => s.SampleId == "SAMPLE002");
        secondSample.Parameter.Should().NotBeNull();
        secondSample.Parameter.Id.Should().Be(2);
        secondSample.Parameter.DescriptionNL.Should().Be("Parameter 2 NL");
        secondSample.IsExtra.Should().BeTrue();
    }

    [Fact]
    public async Task CheckListService_GetQuestions_ReturnsFilteredQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var templateVersionId = 9363;

        // Act
        var result = await _checkListService.GetQuestionsAsync(templateVersionId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var questions = result.ToList();
        questions.Should().Contain(q => q.ItemId == 1 && q.Title == "Question 1");
        questions.Should().Contain(q => q.ItemId == 2 && q.Title == "Question 2");
    }

    [Fact]
    public async Task CheckListService_GetQuestionsWithAnswers_ReturnsQuestionsWithAnswers()
    {
        // Arrange
        await SeedTestDataAsync();
        var templateVersionId = 9363;
        var missionId = "1673";

        // Act
        var result = await _checkListService.GetQuestionsWithAnswersAsync(templateVersionId, missionId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var questionsWithAnswers = result.ToList();
        
        var firstQuestion = questionsWithAnswers.First(q => q.ItemId == 1);
        firstQuestion.Answer.Should().NotBeNull();
        firstQuestion.Answer.Result.Should().Be("Answer 1");
        
        var secondQuestion = questionsWithAnswers.First(q => q.ItemId == 2);
        secondQuestion.Answer.Should().NotBeNull();
        secondQuestion.Answer.Result.Should().Be("Answer 2");
    }

    [Fact]
    public async Task AnswerService_CRUD_Operations_WorkCorrectly()
    {
        // Arrange
        await SeedTestDataAsync();
        var missionId = "1673";

        // Test Read
        var existingAnswers = await _answerService.GetAnswersAsync(missionId, CancellationToken.None);
        existingAnswers.Should().HaveCount(2);

        // Test Create
        var newAnswerDto = new DIFA.ApiClients.FoodNet.Contract.AnswerDto
        {
            TemplateVersionId = 9363,
            QuestionResultId = 3,
            Result = "New Answer",
            ScoreId = 3,
            MissionId = missionId
        };

        var createdAnswer = await _answerService.CreateAnswerAsync(newAnswerDto, CancellationToken.None);
        createdAnswer.Should().NotBeNull();
        createdAnswer.Result.Should().Be("New Answer");

        // Verify creation
        var answersAfterCreate = await _answerService.GetAnswersAsync(missionId, CancellationToken.None);
        answersAfterCreate.Should().HaveCount(3);

        // Test Update
        var answerToUpdate = answersAfterCreate.First(a => a.Result == "Answer 1");
        answerToUpdate.Result = "Updated Answer 1";
        
        var updatedAnswer = await _answerService.UpdateAnswerAsync(1, answerToUpdate, CancellationToken.None);
        updatedAnswer.Result.Should().Be("Updated Answer 1");

        // Test Delete
        await _answerService.DeleteAnswerAsync(1, CancellationToken.None);
        
        var answersAfterDelete = await _answerService.GetAnswersAsync(missionId, CancellationToken.None);
        answersAfterDelete.Should().HaveCount(2);
        answersAfterDelete.Should().NotContain(a => a.Result == "Updated Answer 1");
    }

    [Fact]
    public async Task EndToEnd_MissionWithSamplesAndQuestions_WorksTogether()
    {
        // Arrange
        await SeedTestDataAsync();
        var operatorId = 2087535;
        var missionId = "1673";
        var templateVersionId = 9363;

        // Act - Get mission
        var missionService = new MissionService(FoodNetContext, AlphaContext, Mapper);
        var mission = await missionService.GetMissionByIdAsync(missionId);

        // Act - Get samples for the mission
        var samples = await _sampleService.GetSampleDetailsAsync(operatorId, 12345);

        // Act - Get questions and answers for the mission
        var questionsWithAnswers = await _checkListService.GetQuestionsWithAnswersAsync(templateVersionId, missionId, CancellationToken.None);

        // Assert - Everything is connected properly
        mission.Should().NotBeNull();
        mission.MissionId.Should().Be(1673);
        mission.LceDto.Should().NotBeNull();

        samples.Should().NotBeNull();
        samples.Should().HaveCount(2);
        samples.Should().OnlyContain(s => s.MissionId == 1673);

        questionsWithAnswers.Should().NotBeNull();
        questionsWithAnswers.Should().HaveCount(2);
        questionsWithAnswers.Should().OnlyContain(q => q.Answer != null && q.Answer.MissionId == missionId);
    }
}

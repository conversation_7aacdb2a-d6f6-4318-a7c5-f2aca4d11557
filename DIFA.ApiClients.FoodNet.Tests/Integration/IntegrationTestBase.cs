using AutoMapper;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Entities;
using DIFA.ApiClients.FoodNet.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DIFA.ApiClients.FoodNet.Tests.Integration;

public abstract class IntegrationTestBase : IDisposable
{
    protected readonly ServiceProvider ServiceProvider;
    protected readonly FoodNetContext FoodNetContext;
    protected readonly AlphaContext AlphaContext;
    protected readonly DynamoContext DynamoContext;
    protected readonly IMapper Mapper;
    protected readonly IFoodNetClient Client;

    protected IntegrationTestBase()
    {
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole());
        
        // Add DbContexts with in-memory databases
        services.AddDbContext<FoodNetContext>(options =>
            options.UseInMemoryDatabase(databaseName: $"FoodNet_{Guid.NewGuid()}"));
        
        services.AddDbContext<AlphaContext>(options =>
            options.UseInMemoryDatabase(databaseName: $"Alpha_{Guid.NewGuid()}"));
        
        services.AddDbContext<DynamoContext>(options =>
            options.UseInMemoryDatabase(databaseName: $"Dynamo_{Guid.NewGuid()}"));
        
        // Add AutoMapper
        var mapperConfig = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<LCE, LCEDto>();
            cfg.CreateMap<Mission, MissionDto>()
                .ForMember(dest => dest.MissionId, opt => opt.MapFrom(src => src.MissionId))
                .ForMember(dest => dest.PlannedStartDate, opt => opt.MapFrom(src => src.PlannedStartDateHour))
                .ForMember(dest => dest.PlannedEndDate, opt => opt.MapFrom(src => src.PlannedEndDateHour))
                .ForMember(dest => dest.ActualStartDate, opt => opt.MapFrom(src => src.ActualStartDateHour))
                .ForMember(dest => dest.ActualEndDate, opt => opt.MapFrom(src => src.ActualEndDateHour))
                .ForMember(dest => dest.LCE, opt => opt.MapFrom(src => src.PceCodeOperator));
            cfg.CreateMap<Parameter, ParameterDto>();
            cfg.CreateMap<Sample, SampleDto>();
        });
        
        services.AddSingleton(mapperConfig.CreateMapper());

        // Add services
        services.AddScoped<IMissionService, MissionService>();
        services.AddScoped<ILCEService, LCEService>();
        services.AddScoped<IMatrixService, MatrixService>();
        services.AddScoped<IParameterService, ParameterService>();
        services.AddScoped<ISampleService, SampleService>();
        services.AddScoped<ICheckListService, CheckListService>();
        services.AddScoped<IAnswerService, AnswerService>();
        services.AddScoped<IFoodNetClient, FoodNetClient>();

        ServiceProvider = services.BuildServiceProvider();

        FoodNetContext = ServiceProvider.GetRequiredService<FoodNetContext>();
        AlphaContext = ServiceProvider.GetRequiredService<AlphaContext>();
        DynamoContext = ServiceProvider.GetRequiredService<DynamoContext>();
        Mapper = ServiceProvider.GetRequiredService<IMapper>();
        Client = ServiceProvider.GetRequiredService<IFoodNetClient>();
    }

    protected async Task SeedTestDataAsync()
    {
        // Seed Alpha context
        var lces = new List<LCE>
        {
            new LCE { ServiceCode = "LCE001", DescriptionNl = "LCE Test 1 NL", DescriptionFr = "LCE Test 1 FR" },
            new LCE { ServiceCode = "LCE002", DescriptionNl = "LCE Test 2 NL", DescriptionFr = "LCE Test 2 FR" }
        };

        var pces = new List<PCE>
        {
            new PCE { ServiceCode = "PCE001", DescriptionNl = "PCE Test 1 NL", DescriptionFr = "PCE Test 1 FR" },
            new PCE { ServiceCode = "PCE002", DescriptionNl = "PCE Test 2 NL", DescriptionFr = "PCE Test 2 FR" }
        };

        var parameters = new List<Parameter>
        {
            new Parameter { Id = 1, DescriptionNL = "Parameter 1 NL", DescriptionFR = "Parameter 1 FR" },
            new Parameter { Id = 2, DescriptionNL = "Parameter 2 NL", DescriptionFR = "Parameter 2 FR" }
        };

        var matrixLevel4s = new List<MatrixLevel4>
        {
            new MatrixLevel4 { MatrixNiv4Id = "M4_001", DescriptionNL = "Matrix Level 4 Test 1 NL", DescriptionFR = "Matrix Level 4 Test 1 FR" },
            new MatrixLevel4 { MatrixNiv4Id = "M4_002", DescriptionNL = "Matrix Level 4 Test 2 NL", DescriptionFR = "Matrix Level 4 Test 2 FR" }
        };

        var matrixLevel5s = new List<MatrixLevel5>
        {
            new MatrixLevel5 { MatrixNiv5Id = "M5_001", DescriptionNL = "Matrix Level 5 Test 1 NL", DescriptionFR = "Matrix Level 5 Test 1 FR" },
            new MatrixLevel5 { MatrixNiv5Id = "M5_002", DescriptionNL = "Matrix Level 5 Test 2 NL", DescriptionFR = "Matrix Level 5 Test 2 FR" }
        };

        await AlphaContext.LCEs.AddRangeAsync(lces);
        await AlphaContext.PCEs.AddRangeAsync(pces);
        await AlphaContext.Parameters.AddRangeAsync(parameters);
        await AlphaContext.MatrixLevel4s.AddRangeAsync(matrixLevel4s);
        await AlphaContext.MatrixLevel5s.AddRangeAsync(matrixLevel5s);
        await AlphaContext.SaveChangesAsync();

        // Seed FoodNet context
        var missions = new List<Mission>
        {
            new Mission
            {
                MissionId = 1673,
                MissionNb = "2462/21/083243",
                OperatorId = 2087535,
                Noe = 0,
                PlannedStartDateHour = DateTime.Now.AddDays(-2),
                PlannedEndDateHour = DateTime.Now.AddDays(-1),
                ActualStartDateHour = DateTime.Now.AddDays(-2),
                ActualEndDateHour = DateTime.Now.AddDays(-1),
                PceCodeOperator = "LCE001"
            },
            new Mission
            {
                MissionId = 1674,
                MissionNb = "2462/21/083244",
                OperatorId = 2087535,
                Noe = 0,
                PlannedStartDateHour = DateTime.Now.AddDays(-4),
                PlannedEndDateHour = DateTime.Now.AddDays(-3),
                ActualStartDateHour = DateTime.Now.AddDays(-4),
                ActualEndDateHour = DateTime.Now.AddDays(-3),
                PceCodeOperator = "LCE002"
            }
        };

        var samples = new List<Sample>
        {
            new Sample
            {
                SampleId = "SAMPLE001",
                SampleParameterId = "PARAM001",
                SampleNb = "SN001",
                ControlId = "12345",
                SamplingId = "SAMPLING001",
                ParameterId = 1,
                ParameterResult = "RESULT1",
                SampleResult = "PASS",
                IsExtra = false,
                MissionId = 1673
            },
            new Sample
            {
                SampleId = "SAMPLE002",
                SampleParameterId = "PARAM002",
                SampleNb = "SN002",
                ControlId = "12345",
                SamplingId = "SAMPLING002",
                ParameterId = 2,
                ParameterResult = "RESULT2",
                SampleResult = "FAIL",
                IsExtra = true,
                MissionId = 1673
            }
        };

        var sampleLists = new List<SampleList>
        {
            new SampleList
            {
                MONSTERNUMMER = "SAMPLE001",
                CONTROLE_ID = "12345",
                DATUMMONSTERNAME = DateTime.Now.AddDays(-1),
                RESULTAAT = "PASS",
                MATRIX_NIV_4_ID = "M4_001",
                MATRIX_NIV_5_ID = "M5_001",
                OperatorId = 2087535
            },
            new SampleList
            {
                MONSTERNUMMER = "SAMPLE002",
                CONTROLE_ID = "12346",
                DATUMMONSTERNAME = DateTime.Now.AddDays(-2),
                RESULTAAT = "FAIL",
                MATRIX_NIV_4_ID = "M4_002",
                MATRIX_NIV_5_ID = "M5_002",
                OperatorId = 2087535
            }
        };

        await FoodNetContext.Missions.AddRangeAsync(missions);
        await FoodNetContext.Samples.AddRangeAsync(samples);
        await FoodNetContext.SampleLists.AddRangeAsync(sampleLists);
        await FoodNetContext.SaveChangesAsync();

        // Seed Dynamo context
        var questions = new List<Question>
        {
            new Question
            {
                ItemId = 1,
                ParentChapterTitle = "Parent Chapter 1",
                ParentTitle = "Chapter 1",
                Title = "Question 1",
                QuestionType = "Multiple Choice",
                QuestionTypeId = 1,
                TemplateVersionId = 9363
            },
            new Question
            {
                ItemId = 2,
                ParentChapterTitle = "Parent Chapter 1",
                ParentTitle = "Chapter 1",
                Title = "Question 2",
                QuestionType = "Text",
                QuestionTypeId = 2,
                TemplateVersionId = 9363
            }
        };

        var answers = new List<Answer>
        {
            new Answer
            {
                Id = 1,
                TemplateVersionId = 9363,
                QuestionResultId = 1,
                Result = "Answer 1",
                ScoreId = 1,
                MissionId = "1673"
            },
            new Answer
            {
                Id = 2,
                TemplateVersionId = 9363,
                QuestionResultId = 2,
                Result = "Answer 2",
                ScoreId = 2,
                MissionId = "1673"
            }
        };

        await DynamoContext.Questions.AddRangeAsync(questions);
        await DynamoContext.Answers.AddRangeAsync(answers);
        await DynamoContext.SaveChangesAsync();
    }

    public virtual void Dispose()
    {
        FoodNetContext?.Dispose();
        AlphaContext?.Dispose();
        DynamoContext?.Dispose();
        ServiceProvider?.Dispose();
    }
}

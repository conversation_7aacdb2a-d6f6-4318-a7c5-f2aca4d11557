using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Integration;

[Trait("Category", "Integration")]
public class MissionServiceIntegrationTests : IntegrationTestBase
{
    // IntegrationTestBase provides Client (FoodNetClient) for testing
}

    [Fact]
    public async Task GetMissionsAsync_WithSeededData_ReturnsCorrectMissions()
    {
        // Arrange
        await SeedTestDataAsync();
        var operatorId = 2087535;

        // Act
        var result = await Client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);

        var missions = result.ToList();
        missions.Should().OnlyContain(m => m.LCE != null);

        var firstMission = missions.First(m => m.MissionId == 1673);
        firstMission.MissionNb.Should().Be("2462/21/083243");
        firstMission.LCE.Should().Be("LCE001");
        firstMission.LceDto.Should().NotBeNull();
        firstMission.LceDto.ServiceCode.Should().Be("LCE001");
        firstMission.LceDto.DescriptionNl.Should().Be("LCE Test 1 NL");

        var secondMission = missions.First(m => m.MissionId == 1674);
        secondMission.MissionNb.Should().Be("2462/21/083244");
        secondMission.LCE.Should().Be("LCE002");
        secondMission.LceDto.Should().NotBeNull();
        secondMission.LceDto.ServiceCode.Should().Be("LCE002");
        secondMission.LceDto.DescriptionNl.Should().Be("LCE Test 2 NL");
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithSeededData_ReturnsCorrectMission()
    {
        // Arrange
        await SeedTestDataAsync();
        var missionId = "1673";

        // Act
        var result = await Client.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().NotBeNull();
        result.MissionId.Should().Be(1673);
        result.MissionNb.Should().Be("2462/21/083243");
        result.LCE.Should().Be("LCE001");
        result.LceDto.Should().NotBeNull();
        result.LceDto.ServiceCode.Should().Be("LCE001");
        result.LceDto.DescriptionNl.Should().Be("LCE Test 1 NL");
        result.LceDto.DescriptionFr.Should().Be("LCE Test 1 FR");
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithNonExistentId_ReturnsNull()
    {
        // Arrange
        await SeedTestDataAsync();
        var missionId = "9999";

        // Act
        var result = await Client.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetMissionsAsync_WithNonExistentOperator_ReturnsEmptyList()
    {
        // Arrange
        await SeedTestDataAsync();
        var operatorId = 9999;

        // Act
        var result = await Client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetMissionsAsync_OrdersByActualStartDateDescending()
    {
        // Arrange
        await SeedTestDataAsync();
        var operatorId = 2087535;

        // Act
        var result = await Client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        var missions = result.ToList();
        missions.Should().HaveCount(2);

        // Verify ordering - more recent mission should come first
        missions[0].MissionId.Should().Be(1673); // More recent mission
        missions[1].MissionId.Should().Be(1674); // Older mission
    }

    [Fact]
    public async Task GetMissionsAsync_FiltersOutMissionsWithNoeNotEqualToZero()
    {
        // Arrange
        await SeedTestDataAsync();
        
        // Add a mission with NOE = 1 (should be filtered out)
        var missionWithNoeOne = new DIFA.ApiClients.FoodNet.Entities.Mission
        {
            MissionId = 1675,
            MissionNb = "2462/21/083245",
            OperatorId = 2087535,
            Noe = 1, // This should cause it to be filtered out
            PlannedStartDateHour = DateTime.Now.AddDays(-1),
            PlannedEndDateHour = DateTime.Now,
            ActualStartDateHour = DateTime.Now.AddDays(-1),
            ActualEndDateHour = DateTime.Now,
            PceCodeOperator = "LCE001"
        };
        
        await FoodNetContext.Missions.AddAsync(missionWithNoeOne);
        await FoodNetContext.SaveChangesAsync();
        
        var operatorId = 2087535;

        // Act
        var result = await Client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2); // Should still only return the 2 missions with NOE = 0
        result.Should().NotContain(m => m.MissionId == 1675);
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithMissionHavingNoeNotEqualToZero_ReturnsNull()
    {
        // Arrange
        await SeedTestDataAsync();

        // Add a mission with NOE = 1
        var missionWithNoeOne = new DIFA.ApiClients.FoodNet.Entities.Mission
        {
            MissionId = 1675,
            MissionNb = "2462/21/083245",
            OperatorId = 2087535,
            Noe = 1, // This should cause it to be filtered out
            PlannedStartDateHour = DateTime.Now.AddDays(-1),
            PlannedEndDateHour = DateTime.Now,
            ActualStartDateHour = DateTime.Now.AddDays(-1),
            ActualEndDateHour = DateTime.Now,
            PceCodeOperator = "LCE001"
        };

        await FoodNetContext.Missions.AddAsync(missionWithNoeOne);
        await FoodNetContext.SaveChangesAsync();

        // Act
        var result = await Client.GetMissionByIdAsync("1675");

        // Assert
        result.Should().BeNull(); // Should be filtered out due to NOE = 1
    }
}

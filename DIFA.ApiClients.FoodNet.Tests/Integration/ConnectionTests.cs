using DIFA.ApiClients.FoodNet.Tests.IntegrationTestSetup;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Xunit.Abstractions;

namespace DIFA.ApiClients.FoodNet.Tests.Integration;

/// <summary>
/// Integration tests that verify database connections and service integrations
/// </summary>
[Trait("Category", "Integration")]
public class ConnectionTests : IClassFixture<InjectionFixture>
{
    private readonly InjectionFixture _fixture;
    private readonly IFoodNetClient _client;

    public ConnectionTests(InjectionFixture fixture, ITestOutputHelper outputHelper)
    {
        _fixture = fixture;
        _fixture.OutputHelper = outputHelper;
        _client = _fixture.ServiceProvider.GetRequiredService<IFoodNetClient>();
    }

    [Fact]
    public async Task FoodNetClient_CanBeResolved_FromServiceProvider()
    {
        // Act & Assert
        _client.Should().NotBeNull();
        _client.Should().BeAssignableTo<IFoodNetClient>();
    }

    [Fact]
    public async Task GetMissionsAsync_WithValidOperatorId_CanConnectToDatabase()
    {
        // Arrange
        var operatorId = 2087535;

        // Act
        var result = await _client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        // Note: Result may be empty if no test data exists, but connection should work
    }

    [Fact]
    public async Task GetLCEsAsync_CanConnectToAlphaDatabase()
    {
        // Act
        var result = await _client.GetLCEsAsync();

        // Assert
        result.Should().NotBeNull();
        // Note: Result may be empty if no test data exists, but connection should work
    }

    [Fact]
    public async Task GetMatricesLevel4Async_CanConnectToAlphaDatabase()
    {
        // Act
        var result = await _client.GetMatricesLevel4Async();

        // Assert
        result.Should().NotBeNull();
        // Note: Result may be empty if no test data exists, but connection should work
    }

    [Fact]
    public async Task GetMatricesLevel5Async_CanConnectToAlphaDatabase()
    {
        // Act
        var result = await _client.GetMatricesLevel5Async();

        // Assert
        result.Should().NotBeNull();
        // Note: Result may be empty if no test data exists, but connection should work
    }

    [Fact]
    public async Task GetParametersAsync_CanConnectToAlphaDatabase()
    {
        // Act
        var result = await _client.GetParametersAsync();

        // Assert
        result.Should().NotBeNull();
        // Note: Result may be empty if no test data exists, but connection should work
    }

    [Fact]
    public async Task GetSamplesAsync_WithValidOperatorId_CanConnectToFoodNetDatabase()
    {
        // Arrange
        var operatorId = 2087535;

        // Act
        var result = await _client.GetSamplesAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        // Note: Result may be empty if no test data exists, but connection should work
    }

    [Fact]
    public async Task GetQuestionsAsync_WithValidTemplateVersionId_CanConnectToDynamoDatabase()
    {
        // Arrange
        var templateVersionId = 9363;

        // Act
        var result = await _client.GetQuestionsAsync(templateVersionId);

        // Assert
        result.Should().NotBeNull();
        // Note: Result may be empty if no test data exists, but connection should work
    }

    [Fact]
    public async Task GetAnswersAsync_WithValidMissionId_CanConnectToDynamoDatabase()
    {
        // Arrange
        var missionId = "1673";

        // Act
        var result = await _client.GetAnswersAsync(missionId);

        // Assert
        result.Should().NotBeNull();
        // Note: Result may be empty if no test data exists, but connection should work
    }

    [Fact]
    public async Task EndToEnd_AllServices_CanBeAccessedThroughClient()
    {
        // Arrange
        var operatorId = 2087535;
        var missionId = "1673";
        var templateVersionId = 9363;

        // Act & Assert - Test that all major service endpoints are accessible
        var missions = await _client.GetMissionsAsync(operatorId);
        missions.Should().NotBeNull();

        var lces = await _client.GetLCEsAsync();
        lces.Should().NotBeNull();

        var matrices4 = await _client.GetMatricesLevel4Async();
        matrices4.Should().NotBeNull();

        var matrices5 = await _client.GetMatricesLevel5Async();
        matrices5.Should().NotBeNull();

        var parameters = await _client.GetParametersAsync();
        parameters.Should().NotBeNull();

        var samples = await _client.GetSamplesAsync(operatorId);
        samples.Should().NotBeNull();

        var questions = await _client.GetQuestionsAsync(templateVersionId);
        questions.Should().NotBeNull();

        var answers = await _client.GetAnswersAsync(missionId);
        answers.Should().NotBeNull();

        // If we get here without exceptions, all database connections are working
    }
}

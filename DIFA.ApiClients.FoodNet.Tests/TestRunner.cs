using Xunit;
using Xunit.Abstractions;

namespace DIFA.ApiClients.FoodNet.Tests;

/// <summary>
/// Test runner utility class for organizing and running different test categories
/// </summary>
public class TestRunner
{
    private readonly ITestOutputHelper _output;

    public TestRunner(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public void RunAllUnitTests()
    {
        _output.WriteLine("Running all unit tests...");
        _output.WriteLine("Unit tests use WireMock to test the FoodNetClient with JSON mock responses.");
        _output.WriteLine("These tests verify the client API without database dependencies.");
        _output.WriteLine("");
        _output.WriteLine("Unit test structure:");
        _output.WriteLine("- All tests go through FoodNetClient interface");
        _output.WriteLine("- WireMock provides JSON responses from Mappings folder");
        _output.WriteLine("- TestBase sets up WireMock server and FoodNetClient");
        _output.WriteLine("- Fast execution with realistic API responses");
        _output.WriteLine("");
        _output.WriteLine("To run unit tests only: dotnet test --filter Category=Unit");
    }

    [Fact]
    public void RunAllIntegrationTests()
    {
        _output.WriteLine("Running all integration tests...");
        _output.WriteLine("Integration tests verify real database connections and service functionality.");
        _output.WriteLine("These tests use InjectionFixture with real database connections and configuration.");
        _output.WriteLine("");
        _output.WriteLine("Integration test structure:");
        _output.WriteLine("- ConnectionTests: Tests actual database connections through FoodNetClient");
        _output.WriteLine("- Uses InjectionFixture for dependency injection and configuration");
        _output.WriteLine("- Loads configuration from appsettings.json and Azure Key Vault");
        _output.WriteLine("- Tests all major service endpoints for connectivity");
        _output.WriteLine("");
        _output.WriteLine("To run integration tests only: dotnet test --filter Category=Integration");
    }

    [Fact]
    public void TestInstructions()
    {
        _output.WriteLine("=== FoodNet API Client Test Suite ===");
        _output.WriteLine("");
        _output.WriteLine("This test suite includes comprehensive unit and integration tests for the FoodNet API client.");
        _output.WriteLine("");
        _output.WriteLine("Test Structure:");
        _output.WriteLine("1. Unit Tests (/Services/*Tests.cs)");
        _output.WriteLine("   - Test individual service methods in isolation");
        _output.WriteLine("   - Use mocking to avoid database dependencies");
        _output.WriteLine("   - Fast execution, focused on business logic");
        _output.WriteLine("");
        _output.WriteLine("2. Integration Tests (/Integration/*Tests.cs)");
        _output.WriteLine("   - Test complete workflows with in-memory databases");
        _output.WriteLine("   - Verify entity mappings and database interactions");
        _output.WriteLine("   - Test service-to-service communication");
        _output.WriteLine("");
        _output.WriteLine("Running Tests:");
        _output.WriteLine("- All tests: dotnet test");
        _output.WriteLine("- Unit tests only: dotnet test --filter Category=Unit");
        _output.WriteLine("- Integration tests only: dotnet test --filter Category=Integration");
        _output.WriteLine("- Specific test class: dotnet test --filter ClassName=MissionServiceTests");
        _output.WriteLine("- Specific test method: dotnet test --filter MethodName=GetMissionsAsync_WithValidOperatorId_ReturnsFilteredMissions");
        _output.WriteLine("");
        _output.WriteLine("Test Coverage:");
        _output.WriteLine("- MissionService: Mission retrieval, LCE mapping, filtering by NOE and operator");
        _output.WriteLine("- MatrixService: Matrix level 4 and 5 retrieval from separate tables");
        _output.WriteLine("- LCEService: Union of LCE and PCE tables with deduplication");
        _output.WriteLine("- ParameterService: Parameter retrieval from ALPHA schema");
        _output.WriteLine("- SampleService: Sample retrieval with parameter and matrix enrichment");
        _output.WriteLine("- CheckListService: Question and answer management");
        _output.WriteLine("- AnswerService: CRUD operations for answers");
        _output.WriteLine("");
        _output.WriteLine("Key Test Scenarios:");
        _output.WriteLine("- Database schema compliance (column mappings, table names)");
        _output.WriteLine("- SQL query logic (filtering, joins, unions)");
        _output.WriteLine("- Entity-to-DTO mapping");
        _output.WriteLine("- Error handling (null values, non-existent records)");
        _output.WriteLine("- Business logic (NOE filtering, operator filtering, deduplication)");
        _output.WriteLine("");
        _output.WriteLine("Prerequisites:");
        _output.WriteLine("- .NET 8.0 SDK");
        _output.WriteLine("- xUnit test framework");
        _output.WriteLine("- Moq for mocking");
        _output.WriteLine("- FluentAssertions for readable assertions");
        _output.WriteLine("- Entity Framework Core In-Memory provider");
        _output.WriteLine("");
        _output.WriteLine("Note: These tests use in-memory databases and do not require actual Oracle/SQL Server connections.");
    }
}

{"FoodNet": {"FoodNetConnectionString": "Data Source=localhost;Initial Catalog=FoodNetTest;Integrated Security=true;TrustServerCertificate=true", "AlphaConnectionString": "Data Source=localhost;Initial Catalog=AlphaTest;Integrated Security=true;TrustServerCertificate=true", "DynamoConnectionString": "Data Source=localhost;Initial Catalog=DynamoTest;Integrated Security=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}
using DIFA.ApiClients.FoodNet.Mock;
using DIFA.ApiClients.FoodNet.Mock.Services;
using Microsoft.Extensions.Logging;
using Moq;
using WireMock.Server;

namespace DIFA.ApiClients.FoodNet.Tests;

/// <summary>
/// Base class for all test classes to reduce code duplication
/// </summary>
public abstract class TestBase : IDisposable
{
    private readonly WireMockServer _mockServer;
    private readonly HttpClient _httpClient;
    
    // HTTP Services for testing
    protected readonly IMissionHttpService MissionHttpService;
    protected readonly ILCEHttpService LCEHttpService;
    protected readonly IMatrixHttpService MatrixHttpService;
    protected readonly IParameterHttpService ParameterHttpService;
    protected readonly ISampleHttpService SampleHttpService;
    protected readonly IQuestionHttpService QuestionHttpService;
    protected readonly IAnswerHttpService AnswerHttpService;

    protected TestBase()
    {
        // Setup WireMock server
        _mockServer = WireMockServer.Start();
        _mockServer.AllowPartialMapping();
        _mockServer.WithMapping(FoodNetMockData.AllMappings);

        // Configure HttpClient to use mock server
        _httpClient = new HttpClient
        {
            BaseAddress = new Uri(_mockServer.Urls[0])
        };

        // Initialize HTTP services
        MissionHttpService = new MissionHttpService(_httpClient);
        LCEHttpService = new LCEHttpService(_httpClient);
        MatrixHttpService = new MatrixHttpService(_httpClient);
        ParameterHttpService = new ParameterHttpService(_httpClient);
        SampleHttpService = new SampleHttpService(_httpClient);
        QuestionHttpService = new QuestionHttpService(_httpClient);
        AnswerHttpService = new AnswerHttpService(_httpClient);
    }

    /// <summary>
    /// Reset the WireMock server to its initial state
    /// </summary>
    protected void ResetMockServer()
    {
        _mockServer.Reset();
        _mockServer.WithMapping(FoodNetMockData.AllMappings);
    }

    /// <summary>
    /// Get the base URL of the mock server for debugging purposes
    /// </summary>
    protected string MockServerUrl => _mockServer.Urls[0];

    public void Dispose()
    {
        _mockServer.Stop();
        _httpClient.Dispose();
        GC.SuppressFinalize(this);
    }
}

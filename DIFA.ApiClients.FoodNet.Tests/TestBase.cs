using DIFA.ApiClients.FoodNet.Mock;
using DIFA.ApiClients.FoodNet.Services;
using DIFA.ApiClients.FoodNet.Contract;
using Moq;
using WireMock.Server;

namespace DIFA.ApiClients.FoodNet.Tests;

/// <summary>
/// Base class for all test classes to reduce code duplication
/// </summary>
public abstract class TestBase : IDisposable
{
    private readonly WireMockServer _mockServer;
    private readonly HttpClient _httpClient;

    // HTTP Services for testing
    protected readonly IMissionHttpService MissionHttpService;
    protected readonly ILCEHttpService LCEHttpService;
    protected readonly IMatrixHttpService MatrixHttpService;
    protected readonly IParameterHttpService ParameterHttpService;
    protected readonly ISampleHttpService SampleHttpService;
    protected readonly IQuestionHttpService QuestionHttpService;
    protected readonly IAnswerHttpService AnswerHttpService;

    // FoodNet Client for testing
    protected readonly IFoodNetClient Client;

    protected TestBase()
    {
        // Setup WireMock server
        _mockServer = WireMockServer.Start();
        _mockServer.AllowPartialMapping();
        _mockServer.WithMapping(FoodNetMockData.AllMappings);

        // Configure HttpClient to use mock server
        _httpClient = new HttpClient
        {
            BaseAddress = new Uri(_mockServer.Urls[0])
        };

        // Initialize HTTP services
        MissionHttpService = new MissionHttpService(_httpClient);
        LCEHttpService = new LCEHttpService(_httpClient);
        MatrixHttpService = new MatrixHttpService(_httpClient);
        ParameterHttpService = new ParameterHttpService(_httpClient);
        SampleHttpService = new SampleHttpService(_httpClient);
        QuestionHttpService = new QuestionHttpService(_httpClient);
        AnswerHttpService = new AnswerHttpService(_httpClient);

        // Create mocked services that delegate to HTTP services
        var mockMissionService = CreateMockMissionService();
        var mockLCEService = CreateMockLCEService();
        var mockMatrixService = CreateMockMatrixService();
        var mockParameterService = CreateMockParameterService();
        var mockSampleService = CreateMockSampleService();
        var mockCheckListService = CreateMockCheckListService();
        var mockAnswerService = CreateMockAnswerService();

        // Create FoodNetClient with mocked services
        Client = new FoodNetClient(
            mockLCEService.Object,
            mockMatrixService.Object,
            mockParameterService.Object,
            mockCheckListService.Object,
            mockMissionService.Object,
            mockSampleService.Object,
            mockAnswerService.Object);
    }

    #region Mock Service Creation Methods

    private Mock<IMissionService> CreateMockMissionService()
    {
        var mock = new Mock<IMissionService>();
        mock.Setup(s => s.GetMissionsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .Returns<int, CancellationToken>(async (operatorId, ct) =>
                await MissionHttpService.GetMissionsAsync(operatorId, ct));

        mock.Setup(s => s.GetMissionByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (missionId, ct) =>
            {
                if (long.TryParse(missionId, out var id))
                    return await MissionHttpService.GetMissionByIdAsync(id, ct);
                return null;
            });

        mock.Setup(s => s.GenerateMissionPdfAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new byte[] { 0x25, 0x50, 0x44, 0x46 }); // Mock PDF bytes

        return mock;
    }

    private Mock<ILCEService> CreateMockLCEService()
    {
        var mock = new Mock<ILCEService>();
        mock.Setup(s => s.GetLCEs(It.IsAny<CancellationToken>()))
            .Returns<CancellationToken>(async (ct) =>
                (await LCEHttpService.GetLCEsAsync(ct)).ToList());

        return mock;
    }

    private Mock<IMatrixService> CreateMockMatrixService()
    {
        var mock = new Mock<IMatrixService>();
        mock.Setup(s => s.GetMatrixLevel4Async(It.IsAny<CancellationToken>()))
            .Returns<CancellationToken>(async (ct) =>
                (await MatrixHttpService.GetMatrixLevel4Async(ct)).ToList());

        mock.Setup(s => s.GetMatrixLevel5Async(It.IsAny<CancellationToken>()))
            .Returns<CancellationToken>(async (ct) =>
                (await MatrixHttpService.GetMatrixLevel5Async(ct)).ToList());

        mock.Setup(s => s.GetMatricesByLevelAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .Returns<int, CancellationToken>(async (level, ct) =>
            {
                return level switch
                {
                    4 => await MatrixHttpService.GetMatrixLevel4Async(ct),
                    5 => await MatrixHttpService.GetMatrixLevel5Async(ct),
                    _ => new List<MatrixDto>()
                };
            });

        mock.Setup(s => s.GetMatrixByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (matrixId, ct) =>
                await MatrixHttpService.GetMatrixByIdAsync(matrixId, ct));

        return mock;
    }

    private Mock<IParameterService> CreateMockParameterService()
    {
        var mock = new Mock<IParameterService>();
        mock.Setup(s => s.GetParametersAsync(It.IsAny<CancellationToken>()))
            .Returns<CancellationToken>(async (ct) =>
                await ParameterHttpService.GetParametersAsync(ct));

        return mock;
    }

    private Mock<ISampleService> CreateMockSampleService()
    {
        var mock = new Mock<ISampleService>();
        mock.Setup(s => s.GetSamplesAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .Returns<int, CancellationToken>(async (operatorId, ct) =>
                await SampleHttpService.GetSampleListAsync(operatorId, ct));

        mock.Setup(s => s.GetSampleDetailsAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .Returns<int, int, CancellationToken>(async (operatorId, controlId, ct) =>
                await SampleHttpService.GetSampleDetailsAsync(operatorId, controlId, ct));

        mock.Setup(s => s.GetSampleByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (sampleId, ct) =>
                await SampleHttpService.GetSampleByIdAsync(sampleId, ct));

        return mock;
    }

    private Mock<ICheckListService> CreateMockCheckListService()
    {
        var mock = new Mock<ICheckListService>();
        mock.Setup(s => s.GetQuestionsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .Returns<int, CancellationToken>(async (templateVersionId, ct) =>
                (await QuestionHttpService.GetQuestionsAsync(templateVersionId, ct)).ToList());

        mock.Setup(s => s.GetQuestionsWithAnswersAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<int, string, CancellationToken>(async (templateVersionId, missionId, ct) =>
                (await QuestionHttpService.GetQuestionsWithAnswersAsync(templateVersionId, missionId, ct)).ToList());

        return mock;
    }

    private Mock<IAnswerService> CreateMockAnswerService()
    {
        var mock = new Mock<IAnswerService>();
        mock.Setup(s => s.GetAnswersAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (missionId, ct) =>
                (await AnswerHttpService.GetAnswersAsync(missionId, ct)).ToList());

        mock.Setup(s => s.GetAnswersByTemplateAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .Returns<int, CancellationToken>(async (templateVersionId, ct) =>
                (await AnswerHttpService.GetAnswersByTemplateAsync(templateVersionId, ct)).ToList());

        mock.Setup(s => s.CreateAnswerAsync(It.IsAny<AnswerDto>(), It.IsAny<CancellationToken>()))
            .Returns<AnswerDto, CancellationToken>(async (answerDto, ct) =>
                await AnswerHttpService.CreateAnswerAsync(answerDto, ct));

        mock.Setup(s => s.UpdateAnswerAsync(It.IsAny<int>(), It.IsAny<AnswerDto>(), It.IsAny<CancellationToken>()))
            .Returns<int, AnswerDto, CancellationToken>(async (answerId, answerDto, ct) =>
                await AnswerHttpService.UpdateAnswerAsync(answerId, answerDto, ct));

        mock.Setup(s => s.DeleteAnswerAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .Returns<int, CancellationToken>(async (answerId, ct) =>
            {
                await AnswerHttpService.DeleteAnswerAsync(answerId, ct);
                return Task.CompletedTask;
            });

        return mock;
    }

    #endregion

    /// <summary>
    /// Reset the WireMock server to its initial state
    /// </summary>
    protected void ResetMockServer()
    {
        _mockServer.Reset();
        _mockServer.WithMapping(FoodNetMockData.AllMappings);
    }

    /// <summary>
    /// Get the base URL of the mock server for debugging purposes
    /// </summary>
    protected string MockServerUrl => _mockServer.Urls[0];

    public void Dispose()
    {
        _mockServer.Stop();
        _httpClient.Dispose();
        GC.SuppressFinalize(this);
    }
}

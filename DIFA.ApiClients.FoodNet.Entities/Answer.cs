namespace DIFA.ApiClients.FoodNet.Entities;

public class Answer
{
    public int Id { get; set; } // Primary key
    public int TemplateVersionId { get; set; }
    public int QuestionResultId { get; set; }
    public string Result { get; set; }
    public int ScoreId { get; set; }
    public string MissionId { get; set; }
    
    // Navigation properties (if you want relationships)
    public virtual Question Question { get; set; }
}
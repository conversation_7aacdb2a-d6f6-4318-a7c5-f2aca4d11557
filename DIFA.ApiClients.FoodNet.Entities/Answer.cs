namespace DIFA.ApiClients.FoodNet.Entities;

public class Answer
{
    public int Id { get; set; } // Maps to vraagresultaat_id (from Results.sql)
    public int TemplateVersionId { get; set; } // Maps to TEMPLATEVERSIE_ID
    public int QuestionResultId { get; set; } // Maps to vraagresultaat_id
    public string Result { get; set; } // Maps to RESULTAAT
    public int ScoreId { get; set; } // Maps to score_id
    public string MissionId { get; set; } // Maps to missionId

    // Navigation properties (if you want relationships)
    public virtual Question Question { get; set; }
}
namespace DIFA.ApiClients.FoodNet.Entities;

public class Sample
{
    public string SampleId { get; set; }
    public string SampleParameterId { get; set; }
    public string SampleNb { get; set; }
    public string ControlId { get; set; }
    public string SamplingId { get; set; }
    public int ParameterId { get; set; }
    public string ParameterResult { get; set; }
    public string SampleResult { get; set; }
    public bool IsExtra { get; set; }
    public long? MissionId { get; set; }

    // Navigation properties
    public virtual Mission Mission { get; set; }
    public virtual Parameter Parameter { get; set; }
}
namespace DIFA.ApiClients.FoodNet.Entities;

public class Sample
{
    public string SampleId { get; init; } // Maps to MONSTERNAME_PARAMETER_ID
    public string SampleParameterId { get; init; } // Maps to MONSTERNAME_PARAMETER_ID_OUDER
    public string SampleNb { get; init; } // Maps to MONSTERNUMMER
    public string ControlId { get; init; } // Maps to CONTROLE_ID
    public string SamplingId { get; init; } // Maps to MONSTERNAME_ID
    public int ParameterId { get; init; } // Maps to PARAMETER_ID_ALPHA
    public string ParameterResult { get; init; } // Maps to PARAMETER_RESULT
    public string SampleResult { get; init; } // Maps to RESULTAAT
    public bool IsExtra { get; init; } // Maps to IS_EXTRA
    public long? MissionId { get; init; } // Maps to MISSIE_ID

    // Navigation properties
    public virtual Mission Mission { get; init; }
    public virtual Parameter Parameter { get; init; }
}
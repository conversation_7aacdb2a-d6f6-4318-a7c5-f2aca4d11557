namespace DIFA.ApiClients.FoodNet.Entities;

public class Sample
{
    public string SampleId { get; set; } // Maps to MONSTERNAME_PARAMETER_ID
    public string SampleParameterId { get; set; } // Maps to MONSTERNAME_PARAMETER_ID_OUDER
    public string SampleNb { get; set; } // Maps to MONSTERNUMMER
    public string ControlId { get; set; } // Maps to CONTROLE_ID
    public string SamplingId { get; set; } // Maps to MONSTERNAME_ID
    public int ParameterId { get; set; } // Maps to PARAMETER_ID_ALPHA
    public string ParameterResult { get; set; } // Maps to PARAMETER_RESULT
    public string SampleResult { get; set; } // Maps to RESULTAAT
    public bool IsExtra { get; set; } // Maps to IS_EXTRA
    public long? MissionId { get; set; } // Maps to MISSIE_ID

    // Navigation properties
    public virtual Mission Mission { get; set; }
    public virtual Parameter Parameter { get; set; }
}
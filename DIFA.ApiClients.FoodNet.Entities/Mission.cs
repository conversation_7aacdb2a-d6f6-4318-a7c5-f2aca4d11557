namespace DIFA.ApiClients.FoodNet.Entities;

public class Mission
{
    public long MissionId { get; set; }
    public string MissionNb { get; set; }
    public DateTime? PlannedStartDateHour { get; set; }
    public DateTime? PlannedEndDateHour { get; set; }
    public DateTime? ActualStartDateHour { get; set; }
    public DateTime? ActualEndDateHour { get; set; }
    public DateTime? ClosingDate { get; set; }
    public long? OperatorId { get; set; }
    public int Noe { get; set; }
    public long? PostalCodeIdAdresControle { get; set; }
    public string PceCodeOperator { get; set; } // This maps to PCE_CONTROLEUR in database
    public string PceCodeAddressControle { get; set; }
    public int Secret { get; set; }

    // Navigation properties
    public virtual ICollection<Sample> Samples { get; set; } = new List<Sample>();
}
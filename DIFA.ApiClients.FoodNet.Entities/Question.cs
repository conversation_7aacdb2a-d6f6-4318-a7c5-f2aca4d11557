namespace DIFA.ApiClients.FoodNet.Entities;

public class Question
{
    public int ItemId { get; init; } // Maps to item_id (from Questions.sql)
    public string ParentChapterTitle { get; init; } // Maps to hoofdstuk_ouder_titel
    public string ParentTitle { get; init; } // Maps to hoofdstuk_titel
    public string Title { get; init; } // Maps to titel
    public string QuestionType { get; init; } // Maps to type_vraag_omschrijving
    public int QuestionTypeId { get; init; } // Maps to type_antwoord_id
    public int TemplateVersionId { get; init; } // For filtering by template version
}
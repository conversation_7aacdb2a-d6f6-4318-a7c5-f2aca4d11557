namespace DIFA.ApiClients.FoodNet.Entities;

public class Question
{
    public int ItemId { get; set; } // Maps to item_id (from Questions.sql)
    public string ParentChapterTitle { get; set; } // Maps to hoofdstuk_ouder_titel
    public string ParentTitle { get; set; } // Maps to hoofdstuk_titel
    public string Title { get; set; } // Maps to titel
    public string QuestionType { get; set; } // Maps to type_vraag_omschrijving
    public int QuestionTypeId { get; set; } // Maps to type_antwoord_id
    public int TemplateVersionId { get; set; } // For filtering by template version
}
<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="jsreport.AspNetCore" Version="3.8.1" />
        <PackageReference Include="jsreport.Client" Version="3.8.1" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.2"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.2" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0"/>
        <PackageReference Include="Oracle.EntityFrameworkCore" Version="9.23.60" />
        <PackageReference Include="iTextSharp.LGPLv2.Core" Version="3.7.1" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Samples\Sample.sql" />
      <EmbeddedResource Include="Samples\Sample.sql">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </EmbeddedResource>
      <None Remove="Missions\mission.sql" />
      <EmbeddedResource Include="Missions\Mission.sql">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </EmbeddedResource>
      <None Remove="LCE\LCE.sql" />
      <EmbeddedResource Include="LCE\LCE.sql">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </EmbeddedResource>
      <None Remove="Missions\MissionList.sql" />
      <EmbeddedResource Include="Missions\MissionList.sql" />
      <None Remove="Samples\SampleList.sql" />
      <EmbeddedResource Include="Samples\SampleList.sql">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </EmbeddedResource>
      <None Remove="Matrix\MatrixLevel1.sql" />
      <None Remove="Matrix\MatrixLevel2.sql" />
      <None Remove="Matrix\MatrixLevel3.sql" />
      <None Remove="Matrix\MatrixLevel4.sql" />
      <EmbeddedResource Include="Matrix\MatrixLevel4.sql" />
      <None Remove="Matrix\MatrixLevel5.sql" />
      <EmbeddedResource Include="Matrix\MatrixLevel5.sql" />
      <None Remove="Parameters\Parameter.sql" />
      <EmbeddedResource Include="Parameters\Parameter.sql" />
      <None Remove="Missions\examplepdf\converted-pdf.html" />
      <EmbeddedResource Include="Missions\examplepdf\converted-pdf.html" />
      <None Remove="Missions\examplepdf\example-inline-css.html" />
      <EmbeddedResource Include="Missions\examplepdf\example-inline-css.html" />
      <None Remove="Checklist\Questions.sql" />
      <EmbeddedResource Include="Checklist\Questions.sql" />
    </ItemGroup>

</Project>
